#!/usr/bin/env python3
"""
检查GUI应用中数据库路径配置的脚本
"""

import os
import sys
import sqlite3

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_gui_database_config():
    """检查GUI数据库配置"""
    print("=" * 60)
    print("GUI数据库配置检查")
    print("=" * 60)
    
    try:
        from gui.database_manager import DatabaseManager
        from gui.config import get_config
        
        # 检查配置文件中的数据库路径
        config_db_path = get_config('database.path', 'data.db')
        print(f"配置文件中的数据库路径: {config_db_path}")
        
        # 创建数据库管理器实例
        db_manager = DatabaseManager()
        print(f"数据库管理器使用的路径: {db_manager.db_path}")
        print(f"数据库文件绝对路径: {os.path.abspath(db_manager.db_path)}")
        
        # 检查数据库文件是否存在
        if os.path.exists(db_manager.db_path):
            file_size = os.path.getsize(db_manager.db_path) / 1024 / 1024
            print(f"数据库文件存在，大小: {file_size:.2f} MB")
            
            # 检查数据库中的表
            conn = sqlite3.connect(db_manager.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"数据库中的表数量: {len(tables)}")
            
            if tables:
                print("前10个表:")
                for table in tables[:10]:
                    cursor.execute(f"SELECT COUNT(*) FROM [{table}]")
                    count = cursor.fetchone()[0]
                    print(f"  - {table}: {count} 条记录")
            
            conn.close()
        else:
            print("数据库文件不存在")
        
        return True
        
    except Exception as e:
        print(f"检查GUI数据库配置失败: {e}")
        return False

def check_all_database_files():
    """检查项目中所有的数据库文件"""
    print("\n" + "=" * 60)
    print("项目中所有数据库文件检查")
    print("=" * 60)
    
    # 要检查的数据库文件路径
    db_paths = [
        'data.db',  # 项目根目录
        'gui/data.db',  # GUI目录
        'market_data/data.db'  # 市场数据目录
    ]
    
    for db_path in db_paths:
        print(f"\n检查: {db_path}")
        abs_path = os.path.abspath(db_path)
        print(f"绝对路径: {abs_path}")
        
        if os.path.exists(db_path):
            file_size = os.path.getsize(db_path) / 1024 / 1024
            print(f"✓ 文件存在，大小: {file_size:.2f} MB")
            
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
                tables = [row[0] for row in cursor.fetchall()]
                print(f"  表数量: {len(tables)}")
                
                total_records = 0
                for table in tables:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM [{table}]")
                        count = cursor.fetchone()[0]
                        total_records += count
                    except:
                        pass
                
                print(f"  总记录数: {total_records}")
                conn.close()
                
            except Exception as e:
                print(f"  ✗ 数据库访问错误: {e}")
        else:
            print("✗ 文件不存在")

def check_script_database_usage():
    """检查脚本中的数据库使用情况"""
    print("\n" + "=" * 60)
    print("脚本数据库使用情况检查")
    print("=" * 60)
    
    # 检查几个示例脚本的数据库路径
    script_examples = [
        'basic_data/stock_data.py',
        'market_data/daily.py',
        'special_data/cyq_chips.py'
    ]
    
    for script_path in script_examples:
        print(f"\n检查脚本: {script_path}")
        if os.path.exists(script_path):
            try:
                with open(script_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找数据库路径相关的代码
                lines = content.split('\n')
                db_lines = []
                for i, line in enumerate(lines, 1):
                    if 'data.db' in line or 'db_path' in line:
                        db_lines.append(f"  第{i}行: {line.strip()}")
                
                if db_lines:
                    print("  数据库相关代码:")
                    for line in db_lines[:5]:  # 只显示前5行
                        print(line)
                else:
                    print("  未找到明显的数据库路径配置")
                    
            except Exception as e:
                print(f"  读取文件失败: {e}")
        else:
            print("  文件不存在")

def main():
    """主函数"""
    print("数据库路径配置检查工具")
    print("检查GUI应用获取的数据保存在哪个数据库文件中")
    
    # 执行各项检查
    check_gui_database_config()
    check_all_database_files()
    check_script_database_usage()
    
    print("\n" + "=" * 60)
    print("总结")
    print("=" * 60)
    print("根据检查结果:")
    print("1. GUI应用使用的数据库管理器指向项目根目录的 data.db")
    print("2. 所有通过GUI运行的脚本都会将数据保存到根目录的 data.db 中")
    print("3. gui/data.db 和 market_data/data.db 可能是独立的测试文件")
    print("4. 主要的股票数据都保存在根目录的 data.db 文件中")

if __name__ == "__main__":
    main()
