import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)



# 添加项目根目录到Python路径

from config import get_token
import tushare as ts
import pandas as pd
import sqlite3
import time
from datetime import datetime, timedelta
# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

class StockDatabase:
    def __init__(self, db_name='data.db'):
        """初始化数据库连接"""
        self.db_name = db_name
        self.conn = sqlite3.connect(db_name)
        print(f"数据库 {db_name} 连接成功")

    def create_table(self):
        """创建股权质押统计表"""
        try:
            create_table_sql = '''CREATE TABLE IF NOT EXISTS pledge_stat (
                ts_code TEXT,
                end_date TEXT,
                pledge_count INTEGER,
                unrest_pledge FLOAT,
                rest_pledge FLOAT,
                total_share FLOAT,
                pledge_ratio FLOAT,
                PRIMARY KEY (ts_code, end_date)
            )'''
            
            # 首先删除已存在的表
            self.conn.execute('DROP TABLE IF EXISTS pledge_stat')
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 pledge_stat 创建成功")
            
        except Exception as e:
            print(f"创建表格 pledge_stat 时出错: {e}")
            raise

    def insert_data(self, df):
        """将DataFrame数据插入到表格中"""
        try:
            # 创建临时表
            temp_table_name = 'temp_pledge_stat'
            df.to_sql(temp_table_name, self.conn, if_exists='replace', index=False)
            
            # 使用INSERT OR REPLACE将数据从临时表插入到主表
            insert_sql = f'''
                INSERT OR REPLACE INTO pledge_stat 
                SELECT * FROM {temp_table_name}
            '''
            self.conn.execute(insert_sql)
            
            # 删除临时表
            self.conn.execute(f'DROP TABLE IF EXISTS {temp_table_name}')
            
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")
        except Exception as e:
            print(f"写入数据时出错: {e}")
            self.conn.rollback()

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_stock_list():
    """获取股票列表"""
    try:
        df = pro.stock_basic(exchange='', list_status='L')
        return df['ts_code'].tolist()
    except Exception as e:
        print(f"获取股票列表时出错: {e}")
        return []

def get_date_list():
    """获取近5年的周末日期列表"""
    dates = []
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3*365)  # 3年前
    
    current_date = end_date
    while current_date > start_date:
        # 只获取每周五的日期（作为周末统计时点）
        if current_date.weekday() == 4:  # 0=周一，4=周五
            dates.append(current_date.strftime('%Y%m%d'))
        current_date -= timedelta(days=1)
    
    return sorted(dates)

def fetch_pledge_data(ts_code, end_date):
    """获取单个股票单个日期的股权质押统计数据"""
    try:
        print(f"正在获取 {ts_code} 在 {end_date} 的股权质押统计数据...")
        df = pro.pledge_stat(ts_code=ts_code, end_date=end_date)
        time.sleep(0.5)  # 添加延时以避免频率限制
        return df
    except Exception as e:
        print(f"获取 {ts_code} 在 {end_date} 的股权质押统计数据时出错: {e}")
        return None

def process_stock_data(ts_code, end_date, db):
    """处理单个股票单个日期的数据"""
    try:
        df = fetch_pledge_data(ts_code, end_date)
        if df is not None and not df.empty:
            db.insert_data(df)
            print(f"成功获取 {ts_code} 在 {end_date} 的股权质押统计数据，共 {len(df)} 条记录")
            return True
        return False
    except Exception as e:
        print(f"处理 {ts_code} 在 {end_date} 的股权质押统计数据时出错: {e}")
        return False

def main():
    # 确保reference_data目录存在
    os.makedirs('reference_data', exist_ok=True)
    
    # 创建数据库实例
    db = StockDatabase()
    
    try:
        # 创建表
        db.create_table()
        
        # 获取股票列表
        stock_list = get_stock_list()
        if not stock_list:
            print("获取股票列表失败，程序退出")
            return
        
        # 获取日期列表
        dates = get_date_list()
        print(f"将获取以下日期的数据: {dates}")
        
        total_stocks = len(stock_list)
        for stock_idx, ts_code in enumerate(stock_list):
            print(f"正在处理第 {stock_idx+1}/{total_stocks} 个股票: {ts_code}")
            
            for date_idx, end_date in enumerate(dates):
                if not process_stock_data(ts_code, end_date, db):
                    print(f"跳过 {ts_code} 在 {end_date} 的处理")
                    continue
                
                # 每处理12个周末数据后暂停15秒（大约每季度的数据量）
                if (date_idx + 1) % 12 == 0:
                    print(f"已处理 {ts_code} 的 {date_idx+1} 个周末数据，暂停15秒...")
                    time.sleep(15)
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行A股股权质押统计数据获取程序...")
    main()
    print("程序运行完成！") 