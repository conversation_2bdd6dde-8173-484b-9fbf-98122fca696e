import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import queue
import os
import sys
import sqlite3
from datetime import datetime
import concurrent.futures
import importlib
import traceback
import time

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

from gui.script_manager import ScriptManager
from gui.database_manager import DatabaseManager
from gui.thread_manager import ThreadManager
from gui.config import get_config, set_config, save_config

class StockDataGUIPanel:
    def __init__(self, root):
        self.root = root
        self.root.title("股票数据获取管理面板")

        # 从配置加载窗口设置
        window_width = get_config('window.width', 1400)
        window_height = get_config('window.height', 900)
        window_x = get_config('window.x', 100)
        window_y = get_config('window.y', 100)
        self.root.geometry(f"{window_width}x{window_height}+{window_x}+{window_y}")
        
        # 初始化管理器
        self.script_manager = ScriptManager()
        self.db_manager = DatabaseManager()
        self.thread_manager = ThreadManager()
        
        # 消息队列用于线程间通信
        self.message_queue = queue.Queue()
        
        # 创建GUI组件
        self.create_widgets()
        
        # 启动消息处理
        self.process_messages()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建顶部控制区域
        self.create_control_area(main_frame)
        
        # 创建中部脚本列表区域
        self.create_script_list_area(main_frame)
        
        # 创建底部日志和状态区域
        self.create_log_area(main_frame)
        
    def create_control_area(self, parent):
        """创建控制区域"""
        control_frame = ttk.LabelFrame(parent, text="控制面板", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行：数据库状态和连接控制
        db_frame = ttk.Frame(control_frame)
        db_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(db_frame, text="数据库状态:").pack(side=tk.LEFT)
        self.db_status_var = tk.StringVar(value="未连接")
        self.db_status_label = ttk.Label(db_frame, textvariable=self.db_status_var, foreground="red")
        self.db_status_label.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Button(db_frame, text="检查数据库连接", command=self.check_database_connection).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(db_frame, text="刷新脚本列表", command=self.refresh_script_list).pack(side=tk.LEFT)
        
        # 第二行：并发控制
        concurrent_frame = ttk.Frame(control_frame)
        concurrent_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(concurrent_frame, text="最大并发数:").pack(side=tk.LEFT)
        self.max_workers_var = tk.IntVar(value=get_config('execution.max_workers', 4))
        max_workers_spinbox = ttk.Spinbox(concurrent_frame, from_=1, to=10, width=5, textvariable=self.max_workers_var)
        max_workers_spinbox.pack(side=tk.LEFT, padx=(5, 20))
        
        # 进度条
        ttk.Label(concurrent_frame, text="总体进度:").pack(side=tk.LEFT)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(concurrent_frame, variable=self.progress_var, length=200)
        self.progress_bar.pack(side=tk.LEFT, padx=(5, 20))
        
        self.progress_label = ttk.Label(concurrent_frame, text="0/0")
        self.progress_label.pack(side=tk.LEFT)
        
        # 第三行：主要控制按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X)
        
        self.run_selected_btn = ttk.Button(button_frame, text="运行选中脚本", command=self.run_selected_scripts)
        self.run_selected_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.run_all_btn = ttk.Button(button_frame, text="运行全部脚本", command=self.run_all_scripts)
        self.run_all_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_selected_btn = ttk.Button(button_frame, text="停止选中任务", command=self.stop_selected_tasks, state=tk.DISABLED)
        self.stop_selected_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_all_btn = ttk.Button(button_frame, text="停止所有任务", command=self.stop_all_tasks, state=tk.DISABLED)
        self.stop_all_btn.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="清除日志", command=self.clear_log).pack(side=tk.LEFT, padx=(0, 10))
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(button_frame, textvariable=self.status_var).pack(side=tk.RIGHT)
        
    def create_script_list_area(self, parent):
        """创建脚本列表区域"""
        list_frame = ttk.LabelFrame(parent, text="数据获取脚本列表", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建树状视图
        columns = ("module", "script", "status", "progress", "last_run", "script_path")
        self.script_tree = ttk.Treeview(list_frame, columns=columns, show="tree headings", height=15)
        
        # 设置列标题
        self.script_tree.heading("#0", text="选择")
        self.script_tree.heading("module", text="模块")
        self.script_tree.heading("script", text="脚本名称")
        self.script_tree.heading("status", text="状态")
        self.script_tree.heading("progress", text="进度")
        self.script_tree.heading("last_run", text="最后运行时间")
        
        # 设置列宽
        self.script_tree.column("#0", width=80)
        self.script_tree.column("module", width=150)
        self.script_tree.column("script", width=250)
        self.script_tree.column("status", width=100)
        self.script_tree.column("progress", width=100)
        self.script_tree.column("last_run", width=150)
        self.script_tree.column("script_path", width=0, minwidth=0)  # 隐藏列
        
        # 添加滚动条
        scrollbar_v = ttk.Scrollbar(list_frame, orient="vertical", command=self.script_tree.yview)
        scrollbar_h = ttk.Scrollbar(list_frame, orient="horizontal", command=self.script_tree.xview)
        self.script_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        # 布局
        self.script_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 绑定事件
        self.script_tree.bind("<Button-1>", self.on_tree_click)
        
    def create_log_area(self, parent):
        """创建日志区域"""
        log_frame = ttk.LabelFrame(parent, text="运行日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建日志文本区域
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=12)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 配置日志文本样式
        self.log_text.tag_configure("INFO", foreground="black")
        self.log_text.tag_configure("SUCCESS", foreground="green")
        self.log_text.tag_configure("ERROR", foreground="red")
        self.log_text.tag_configure("WARNING", foreground="orange")
        
    def check_database_connection(self):
        """检查数据库连接"""
        try:
            if self.db_manager.check_connection():
                self.db_status_var.set("已连接")
                self.db_status_label.configure(foreground="green")
                self.log_message("数据库连接正常", "SUCCESS")
            else:
                self.db_status_var.set("连接失败")
                self.db_status_label.configure(foreground="red")
                self.log_message("数据库连接失败", "ERROR")
        except Exception as e:
            self.db_status_var.set("连接错误")
            self.db_status_label.configure(foreground="red")
            self.log_message(f"检查数据库连接时出错: {e}", "ERROR")
            
    def refresh_script_list(self):
        """刷新脚本列表"""
        try:
            self.log_message("正在刷新脚本列表...", "INFO")
            
            # 清除现有项目
            for item in self.script_tree.get_children():
                self.script_tree.delete(item)
            
            # 获取脚本信息
            scripts = self.script_manager.get_all_scripts()
            
            # 按模块分组显示
            modules = {}
            for script in scripts:
                module = script['module']
                if module not in modules:
                    modules[module] = []
                modules[module].append(script)
            
            # 添加到树状视图
            for module_name, module_scripts in modules.items():
                # 添加模块节点（初始状态为未选中）
                module_item = self.script_tree.insert("", "end", text="☐", values=(module_name, "", "", "", ""))

                # 添加脚本节点
                for script in module_scripts:
                    script_item = self.script_tree.insert(module_item, "end", text="☐",
                                                        values=("", script['name'], "就绪", "", ""))
                    # 存储脚本信息
                    self.script_tree.set(script_item, "script_path", script['path'])

                # 初始化目录状态
                self.update_directory_status(module_item)

            # 展开所有模块
            for item in self.script_tree.get_children():
                self.script_tree.item(item, open=True)
                
            self.log_message(f"已加载 {len(scripts)} 个脚本", "SUCCESS")
            
        except Exception as e:
            self.log_message(f"刷新脚本列表时出错: {e}", "ERROR")

    def on_tree_click(self, event):
        """处理树状视图点击事件"""
        item = self.script_tree.identify("item", event.x, event.y)
        if item:
            # 检查是否点击了复选框区域
            region = self.script_tree.identify("region", event.x, event.y)
            if region == "tree":
                # 检查是否是目录项（父级项目）
                if self.script_tree.get_children(item):
                    # 这是一个目录，切换目录下所有脚本的选中状态
                    self.toggle_directory_selection(item)
                else:
                    # 这是一个脚本文件，只切换单个脚本的状态
                    current_text = self.script_tree.item(item, "text")
                    if current_text == "☐":
                        self.script_tree.item(item, text="☑")
                    elif current_text == "☑":
                        self.script_tree.item(item, text="☐")

                    # 更新父目录的状态
                    parent = self.script_tree.parent(item)
                    if parent:
                        self.update_directory_status(parent)

    def toggle_directory_selection(self, directory_item):
        """切换目录下所有脚本的选中状态"""
        children = self.script_tree.get_children(directory_item)
        if not children:
            return

        # 检查当前目录的状态
        # 如果所有子项都选中，则取消选中所有
        # 如果有未选中的，则选中所有
        all_selected = True
        for child in children:
            if self.script_tree.item(child, "text") != "☑":
                all_selected = False
                break

        # 根据当前状态切换
        new_state = "☐" if all_selected else "☑"

        # 更新所有子项
        for child in children:
            self.script_tree.item(child, text=new_state)

        # 更新目录本身的状态
        self.update_directory_status(directory_item)

        # 记录日志
        action = "取消选中" if all_selected else "选中"
        directory_name = self.script_tree.set(directory_item, "script")
        self.log_message(f"{action}目录 '{directory_name}' 下的 {len(children)} 个脚本", "INFO")

    def update_directory_status(self, directory_item):
        """更新目录的显示状态"""
        children = self.script_tree.get_children(directory_item)
        if not children:
            return

        selected_count = 0
        total_count = len(children)

        for child in children:
            if self.script_tree.item(child, "text") == "☑":
                selected_count += 1

        # 根据选中情况设置目录的显示状态
        if selected_count == 0:
            # 没有选中任何脚本
            directory_text = "☐"
        elif selected_count == total_count:
            # 全部选中
            directory_text = "☑"
        else:
            # 部分选中
            directory_text = "◐"  # 使用半选状态符号

        self.script_tree.item(directory_item, text=directory_text)

        # 更新目录的状态显示
        status_text = f"{selected_count}/{total_count} 已选中"
        self.script_tree.set(directory_item, "status", status_text)

    def get_selected_scripts(self):
        """获取选中的脚本"""
        selected_scripts = []
        for item in self.script_tree.get_children():
            for child in self.script_tree.get_children(item):
                if self.script_tree.item(child, "text") == "☑":
                    script_path = self.script_tree.set(child, "script_path")
                    script_name = self.script_tree.set(child, "script")
                    selected_scripts.append({
                        'item_id': child,
                        'path': script_path,
                        'name': script_name
                    })
        return selected_scripts

    def run_selected_scripts(self):
        """运行选中的脚本"""
        selected_scripts = self.get_selected_scripts()
        if not selected_scripts:
            messagebox.showwarning("警告", "请先选择要运行的脚本")
            return

        self.run_scripts(selected_scripts)

    def run_all_scripts(self):
        """运行所有脚本"""
        all_scripts = []
        for item in self.script_tree.get_children():
            for child in self.script_tree.get_children(item):
                script_path = self.script_tree.set(child, "script_path")
                script_name = self.script_tree.set(child, "script")
                if script_path:  # 确保有有效的脚本路径
                    all_scripts.append({
                        'item_id': child,
                        'path': script_path,
                        'name': script_name
                    })

        if not all_scripts:
            messagebox.showwarning("警告", "没有可运行的脚本")
            return

        self.run_scripts(all_scripts)

    def run_scripts(self, scripts):
        """运行脚本列表"""
        if self.thread_manager.is_running():
            messagebox.showwarning("警告", "已有任务在运行中，请等待完成或停止当前任务")
            return

        # 更新UI状态
        self.run_selected_btn.configure(state=tk.DISABLED)
        self.run_all_btn.configure(state=tk.DISABLED)
        self.stop_selected_btn.configure(state=tk.NORMAL)
        self.stop_all_btn.configure(state=tk.NORMAL)

        # 重置进度
        self.progress_var.set(0)
        self.progress_label.configure(text=f"0/{len(scripts)}")

        # 更新脚本状态
        for script in scripts:
            self.script_tree.set(script['item_id'], "status", "等待中")
            self.script_tree.set(script['item_id'], "progress", "0%")

        self.log_message(f"开始运行 {len(scripts)} 个脚本，最大并发数: {self.max_workers_var.get()}", "INFO")

        # 启动线程管理器
        self.thread_manager.start_execution(scripts, self.max_workers_var.get(), self.message_queue)

    def stop_selected_tasks(self):
        """停止选中的任务"""
        # 获取当前正在运行的选中任务
        running_selected = []
        for item in self.script_tree.get_children():
            for child in self.script_tree.get_children(item):
                if self.script_tree.item(child, "text") == "☑":
                    status = self.script_tree.set(child, "status")
                    if status in ["等待中", "运行中"]:
                        running_selected.append({
                            'item_id': child,
                            'name': self.script_tree.set(child, "script")
                        })

        if not running_selected:
            messagebox.showinfo("提示", "没有选中的正在运行的任务")
            return

        task_names = [task['name'] for task in running_selected]
        if messagebox.askyesno("确认", f"确定要停止以下 {len(running_selected)} 个选中的任务吗？\n\n" + "\n".join(task_names[:5]) + ("..." if len(task_names) > 5 else "")):
            item_ids = [task['item_id'] for task in running_selected]
            stopped_count = self.thread_manager.stop_selected(item_ids)

            # 更新UI状态
            for task in running_selected:
                self.script_tree.set(task['item_id'], "status", "已停止")
                self.script_tree.set(task['item_id'], "progress", "已停止")

            self.log_message(f"已停止 {stopped_count} 个选中的任务", "WARNING")

    def stop_all_tasks(self):
        """停止所有任务"""
        if messagebox.askyesno("确认", "确定要停止所有正在运行的任务吗？"):
            self.thread_manager.stop_all()
            self.log_message("正在停止所有任务...", "WARNING")

            # 更新所有运行中任务的状态
            for item in self.script_tree.get_children():
                for child in self.script_tree.get_children(item):
                    status = self.script_tree.set(child, "status")
                    if status in ["等待中", "运行中"]:
                        self.script_tree.set(child, "status", "已停止")
                        self.script_tree.set(child, "progress", "已停止")

    def clear_log(self):
        """清除日志"""
        self.log_text.delete(1.0, tk.END)

    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_line = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_line, level)
        self.log_text.see(tk.END)

    def process_messages(self):
        """处理来自工作线程的消息"""
        try:
            while True:
                message = self.message_queue.get_nowait()
                self.handle_message(message)
        except queue.Empty:
            pass
        finally:
            # 每100ms检查一次消息队列
            self.root.after(100, self.process_messages)

    def handle_message(self, message):
        """处理消息"""
        msg_type = message.get('type')

        if msg_type == 'script_start':
            item_id = message.get('item_id')
            script_name = message.get('script_name')
            self.script_tree.set(item_id, "status", "运行中")
            self.script_tree.set(item_id, "progress", "运行中...")
            self.log_message(f"开始运行: {script_name}", "INFO")

        elif msg_type == 'script_complete':
            item_id = message.get('item_id')
            script_name = message.get('script_name')
            success = message.get('success')
            result_message = message.get('message', '')

            if success:
                self.script_tree.set(item_id, "status", "完成")
                self.script_tree.set(item_id, "progress", "100%")
                self.script_tree.set(item_id, "last_run", datetime.now().strftime("%H:%M:%S"))
                self.log_message(f"完成: {script_name} - {result_message}", "SUCCESS")
            else:
                self.script_tree.set(item_id, "status", "失败")
                self.script_tree.set(item_id, "progress", "失败")
                self.log_message(f"失败: {script_name} - {result_message}", "ERROR")

        elif msg_type == 'progress_update':
            completed = message.get('completed')
            total = message.get('total')
            progress = (completed / total) * 100 if total > 0 else 0
            self.progress_var.set(progress)
            self.progress_label.configure(text=f"{completed}/{total}")
            self.status_var.set(f"进行中: {completed}/{total}")

        elif msg_type == 'all_complete':
            self.run_selected_btn.configure(state=tk.NORMAL)
            self.run_all_btn.configure(state=tk.NORMAL)
            self.stop_selected_btn.configure(state=tk.DISABLED)
            self.stop_all_btn.configure(state=tk.DISABLED)
            self.status_var.set("就绪")

            completed = message.get('completed')
            total = message.get('total')
            failed = message.get('failed')
            self.log_message(f"所有任务完成! 成功: {completed-failed}/{total}, 失败: {failed}/{total}", "SUCCESS")

        elif msg_type == 'log':
            level = message.get('level', 'INFO')
            text = message.get('message', '')
            self.log_message(text, level)

    def on_closing(self):
        """关闭应用程序时的处理"""
        if self.thread_manager.is_running():
            if messagebox.askyesno("确认", "还有任务在运行中，确定要退出吗？"):
                self.thread_manager.stop_all()
                # 保存配置
                self.save_window_config()
                # 等待一段时间让线程清理
                self.root.after(1000, self.root.destroy)
            return

        # 保存配置
        self.save_window_config()
        self.root.destroy()

    def save_window_config(self):
        """保存窗口配置"""
        try:
            # 获取当前窗口位置和大小
            geometry = self.root.geometry()
            # 解析geometry字符串 (例如: "1400x900+100+100")
            size_pos = geometry.split('+')
            size = size_pos[0].split('x')

            if len(size) == 2 and len(size_pos) >= 3:
                set_config('window.width', int(size[0]))
                set_config('window.height', int(size[1]))
                set_config('window.x', int(size_pos[1]))
                set_config('window.y', int(size_pos[2]))

            # 保存执行配置
            set_config('execution.max_workers', self.max_workers_var.get())

            # 保存配置到文件
            save_config()
        except Exception as e:
            print(f"保存配置时出错: {e}")
