#!/usr/bin/env python3
"""
创建测试进度数据
"""

from resume_manager import ResumeManager
import os

def main():
    print("🔧 创建测试进度数据...")
    
    try:
        manager = ResumeManager()
        print(f"📍 数据库路径: {manager.progress_db_path}")
        
        # 创建测试数据
        test_scripts = [
            {
                'name': 'daily.py',
                'status': 'completed',
                'total': 5413,
                'completed': 5413,
                'failed': 0
            },
            {
                'name': 'daily_basic.py', 
                'status': 'completed',
                'total': 5413,
                'completed': 5200,
                'failed': 213
            },
            {
                'name': 'pro_bar.py',
                'status': 'interrupted', 
                'total': 5413,
                'completed': 828,
                'failed': 15
            },
            {
                'name': 'adj_factor.py',
                'status': 'running',
                'total': 5413, 
                'completed': 1500,
                'failed': 5
            },
            {
                'name': 'stock_basic.py',
                'status': 'completed',
                'total': 1,
                'completed': 1,
                'failed': 0
            }
        ]
        
        for script in test_scripts:
            manager.save_script_progress(
                script_name=script['name'],
                status=script['status'],
                total_items=script['total'],
                completed_items=script['completed'],
                failed_items=script['failed'],
                last_processed_item=f"00{script['completed']:04d}.SZ",
                notes=f"测试数据 - {script['status']}"
            )
            print(f"✅ 创建了 {script['name']} 的进度记录")
        
        # 验证数据
        all_progress = manager.get_all_progress()
        print(f"\n📊 验证结果: 共 {len(all_progress)} 条进度记录")
        
        for progress in all_progress:
            print(f"  {progress['script_name']}: {progress['status']} - {progress['progress_percent']}%")
        
        print("\n✅ 测试数据创建完成!")
        print("现在可以重新启动GUI应用查看进度显示")
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
