#!/usr/bin/env python3
"""
测试目录多选功能的脚本
"""

import os
import sys
import time

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_directory_selection_methods():
    """测试目录选择相关方法"""
    try:
        import tkinter as tk
        from gui.main_panel import StockDataGUIPanel
        
        print("测试目录选择功能...")
        
        # 创建根窗口但不显示
        root = tk.Tk()
        root.withdraw()
        
        # 创建GUI实例
        app = StockDataGUIPanel(root)
        
        # 检查新增的方法是否存在
        methods_to_check = [
            'toggle_directory_selection',
            'update_directory_status'
        ]
        
        for method_name in methods_to_check:
            if hasattr(app, method_name):
                print(f"✓ {method_name} 方法存在")
            else:
                print(f"✗ {method_name} 方法不存在")
                return False
        
        # 销毁窗口
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_click_handling():
    """测试点击处理逻辑"""
    try:
        import tkinter as tk
        from gui.main_panel import StockDataGUIPanel
        
        print("\n测试点击处理逻辑...")
        
        # 创建根窗口但不显示
        root = tk.Tk()
        root.withdraw()
        
        # 创建GUI实例
        app = StockDataGUIPanel(root)
        
        # 检查on_tree_click方法是否存在
        if hasattr(app, 'on_tree_click'):
            print("✓ on_tree_click 方法存在")
        else:
            print("✗ on_tree_click 方法不存在")
            return False
        
        # 销毁窗口
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_checkbox_symbols():
    """测试复选框符号"""
    print("\n测试复选框符号...")
    
    symbols = {
        "☐": "未选中",
        "☑": "已选中", 
        "◐": "部分选中"
    }
    
    for symbol, description in symbols.items():
        print(f"✓ {symbol} - {description}")
    
    return True

def simulate_directory_selection():
    """模拟目录选择操作"""
    print("\n模拟目录选择操作...")
    
    # 模拟目录结构
    directory_structure = {
        "基础数据": ["top_list.py", "concept.py", "pledge_detail.py"],
        "实时数据": ["realtime_list.py", "realtime_quote.py"],
        "历史数据": ["daily.py", "monthly.py", "weekly.py"]
    }
    
    print("模拟的目录结构:")
    for dir_name, scripts in directory_structure.items():
        print(f"  📁 {dir_name} ({len(scripts)} 个脚本)")
        for script in scripts:
            print(f"    📄 {script}")
    
    print("\n模拟操作:")
    print("1. 点击 '基础数据' 目录 → 选中该目录下所有 3 个脚本")
    print("2. 再次点击 '基础数据' 目录 → 取消选中该目录下所有脚本")
    print("3. 单独选中 'daily.py' → 目录显示部分选中状态 ◐")
    
    return True

def main():
    """主测试函数"""
    print("=" * 60)
    print("目录多选功能测试")
    print("=" * 60)
    
    tests = [
        ("目录选择方法", test_directory_selection_methods),
        ("点击处理逻辑", test_click_handling),
        ("复选框符号", test_checkbox_symbols),
        ("目录选择模拟", simulate_directory_selection)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
            print(f"✓ {test_name} 测试通过")
        else:
            print(f"✗ {test_name} 测试失败")
    
    print(f"\n结果: {passed}/{len(tests)} 测试通过")
    
    if passed == len(tests):
        print("\n🎉 目录多选功能已成功添加！")
        print("\n新功能说明:")
        print("=" * 40)
        print("📁 目录操作:")
        print("  • 点击目录左边的方块 → 选中/取消选中该目录下所有脚本")
        print("  • 目录状态显示:")
        print("    ☐ - 没有选中任何脚本")
        print("    ☑ - 选中了所有脚本")
        print("    ◐ - 部分脚本被选中")
        print("  • 状态栏显示: '3/5 已选中' (选中数量/总数量)")
        print("\n📄 脚本操作:")
        print("  • 点击脚本左边的方块 → 选中/取消选中单个脚本")
        print("  • 自动更新父目录的状态")
        print("\n💡 使用技巧:")
        print("  • 快速选中整个目录的所有脚本")
        print("  • 精确控制单个脚本的选择")
        print("  • 直观查看每个目录的选中状态")
        print("\n现在可以启动GUI测试这个功能了!")
        print("python start_gui.py")
    else:
        print("\n❌ 部分测试失败，请检查错误信息。")

if __name__ == "__main__":
    main()
