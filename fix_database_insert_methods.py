#!/usr/bin/env python3
"""
批量修复数据库插入方法，解决UNIQUE constraint错误
"""

import os
import re
import glob

def find_problematic_scripts():
    """查找使用简单append模式的脚本"""
    problematic_scripts = []
    
    # 搜索所有Python文件
    for root, dirs, files in os.walk('.'):
        # 跳过一些目录
        if any(skip in root for skip in ['__pycache__', '.git', 'tosharepro', 'gui']):
            continue
            
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 查找使用append模式的to_sql调用
                    if "to_sql(" in content and "if_exists='append'" in content:
                        # 检查是否已经有INSERT OR REPLACE逻辑
                        if "INSERT OR REPLACE" not in content:
                            problematic_scripts.append(file_path)
                            
                except Exception as e:
                    print(f"读取文件 {file_path} 时出错: {e}")
    
    return problematic_scripts

def analyze_script_structure(file_path):
    """分析脚本结构，提取insert_data方法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找insert_data方法
        pattern = r'def insert_data\(self, df.*?\):(.*?)(?=\n    def|\n\nclass|\n\ndef|\Z)'
        match = re.search(pattern, content, re.DOTALL)
        
        if match:
            method_content = match.group(1)
            # 查找表名
            table_pattern = r"to_sql\('(\w+)'"
            table_match = re.search(table_pattern, method_content)
            table_name = table_match.group(1) if table_match else None
            
            return {
                'has_insert_data': True,
                'method_content': method_content,
                'table_name': table_name,
                'full_match': match.group(0)
            }
        
        return {'has_insert_data': False}
        
    except Exception as e:
        print(f"分析文件 {file_path} 时出错: {e}")
        return {'has_insert_data': False, 'error': str(e)}

def generate_fixed_insert_method(table_name):
    """生成修复后的insert_data方法"""
    return f'''    def insert_data(self, df):
        """将DataFrame数据插入到表格中"""
        try:
            if df is None or df.empty:
                print("没有数据需要写入")
                return
                
            # 创建临时表
            temp_table_name = 'temp_{table_name}'
            df.to_sql(temp_table_name, self.conn, if_exists='replace', index=False)
            
            # 使用INSERT OR REPLACE将数据从临时表插入到主表
            insert_sql = f'''
                INSERT OR REPLACE INTO {table_name} 
                SELECT * FROM {{temp_table_name}}
            '''
            self.conn.execute(insert_sql)
            
            # 删除临时表
            self.conn.execute(f'DROP TABLE IF EXISTS {{temp_table_name}}')
            
            self.conn.commit()
            print(f"成功写入 {{len(df)}} 条记录")
        except Exception as e:
            print(f"写入数据时出错: {{e}}")
            self.conn.rollback()'''

def main():
    """主函数"""
    print("=" * 60)
    print("数据库插入方法修复工具")
    print("=" * 60)
    
    # 查找有问题的脚本
    print("正在查找使用简单append模式的脚本...")
    problematic_scripts = find_problematic_scripts()
    
    if not problematic_scripts:
        print("✓ 没有发现需要修复的脚本")
        return
    
    print(f"发现 {len(problematic_scripts)} 个需要修复的脚本:")
    
    for script in problematic_scripts:
        print(f"\n分析脚本: {script}")
        analysis = analyze_script_structure(script)
        
        if analysis['has_insert_data']:
            table_name = analysis['table_name']
            if table_name:
                print(f"  - 表名: {table_name}")
                print(f"  - 需要修复: 使用简单append模式")
                
                # 生成修复建议
                fixed_method = generate_fixed_insert_method(table_name)
                print(f"  - 修复方法已生成")
            else:
                print(f"  - 警告: 无法确定表名")
        else:
            print(f"  - 未找到insert_data方法")
    
    print(f"\n总结:")
    print(f"- 发现 {len(problematic_scripts)} 个脚本使用简单append模式")
    print(f"- 这些脚本可能遇到UNIQUE constraint错误")
    print(f"- 建议使用INSERT OR REPLACE模式来避免冲突")
    
    print(f"\n已修复的脚本示例:")
    print(f"- market_data/adj_factor.py (已修复)")
    
    print(f"\n需要手动修复的脚本:")
    for script in problematic_scripts:
        analysis = analyze_script_structure(script)
        if analysis['has_insert_data'] and analysis['table_name']:
            print(f"- {script} (表: {analysis['table_name']})")

if __name__ == "__main__":
    main()
