import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)



# 添加项目根目录到Python路径

from config import get_token
import tushare as ts
import pandas as pd
import sqlite3
import time
from datetime import datetime
# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

class StockDatabase:
    def __init__(self, db_name='data.db'):
        """初始化数据库连接"""
        self.db_name = db_name
        self.conn = sqlite3.connect(db_name)
        print(f"数据库 {db_name} 连接成功")

    def create_table(self):
        """创建实时成交表"""
        create_table_sql = '''CREATE TABLE IF NOT EXISTS realtime_tick (
            ts_code TEXT,
            trade_date TEXT,
            time TEXT,
            price REAL,
            change REAL,
            volume INTEGER,
            amount INTEGER,
            type TEXT,
            data_source TEXT,
            PRIMARY KEY (ts_code, trade_date, time, data_source)
        )'''
        
        try:
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 realtime_tick 创建成功")
        except Exception as e:
            print(f"创建表格 realtime_tick 时出错: {e}")

    def check_data_exists(self, ts_code, trade_date):
        """检查指定股票和日期的数据是否已存在"""
        try:
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT COUNT(*) FROM realtime_tick
                WHERE ts_code = ? AND trade_date = ?
            ''', (ts_code, trade_date))
            count = cursor.fetchone()[0]
            return count > 0
        except Exception as e:
            print(f"检查数据是否存在时出错: {e}")
            return False

    def insert_data(self, df, ts_code, source):
        """将DataFrame数据插入到表格中"""
        try:
            if df is None or df.empty:
                print("没有数据需要写入")
                return

            # 添加股票代码、交易日期和数据源信息
            trade_date = datetime.now().strftime('%Y%m%d')
            df['ts_code'] = ts_code
            df['trade_date'] = trade_date
            df['data_source'] = source

            # 检查数据是否已存在
            if self.check_data_exists(ts_code, trade_date):
                print(f"数据已存在: {ts_code} ({trade_date})，跳过更新")
                return

            # 尝试插入数据
            df.to_sql('realtime_tick', self.conn, if_exists='append', index=False)
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")

        except Exception as e:
            error_msg = str(e)
            if "UNIQUE constraint failed" in error_msg:
                print(f"数据已存在，跳过更新: {error_msg}")
            else:
                print(f"写入数据时出错: {e}")
                self.conn.rollback()

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_stock_list():
    """获取股票列表"""
    try:
        df = pro.stock_basic(exchange='', list_status='L', 
                            fields='ts_code,symbol,name,area,industry,list_date')
        return df
    except Exception as e:
        print(f"获取股票列表时出错: {e}")
        return None

def fetch_realtime_tick(ts_code, source='sina'):
    """获取单个股票的实时成交数据"""
    try:
        df = ts.realtime_tick(ts_code=ts_code, src=source)
        # 添加延时以避免频率限制
        time.sleep(0.5)
        return df
    except Exception as e:
        print(f"获取 {ts_code} 的实时成交数据时出错: {e}")
        return None

def process_stock_data(ts_code, db, source):
    """处理单个股票的实时成交数据"""
    try:
        print(f"正在获取 {ts_code} 的实时成交数据...")
        df = fetch_realtime_tick(ts_code, source)
        if df is not None and not df.empty:
            db.insert_data(df, ts_code, source)
            print(f"成功获取 {ts_code} 的实时成交数据，共 {len(df)} 条记录")
        return True
    except Exception as e:
        print(f"处理 {ts_code} 的实时成交数据时出错: {e}")
        return False

def main():
    # 确保market_data目录存在
    os.makedirs('market_data', exist_ok=True)
    
    # 创建数据库实例
    db = StockDatabase()
    db.create_table()
    
    try:
        # 获取股票列表
        stock_list = get_stock_list()
        if stock_list is None:
            return
        
        # 设置数据源
        sources = ['sina', 'dc']
        
        total_stocks = len(stock_list)
        for source in sources:
            print(f"\n开始获取{source}数据源的实时成交数据...")
            
            for idx, row in stock_list.iterrows():
                ts_code = row['ts_code']
                print(f"正在处理第 {idx+1}/{total_stocks} 只股票: {ts_code}")
                
                if not process_stock_data(ts_code, db, source):
                    print(f"跳过 {ts_code} 的处理")
                    continue
                
                # 由于实时成交数据量较大，每只股票处理完后多等待一下
                time.sleep(1)
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行A股实时成交数据获取程序...")
    main()
    print("程序运行完成！") 