#!/usr/bin/env python3
"""
最终验证所有数据库脚本修复状态
"""

import os
import re

def detailed_check_script(script_path):
    """详细检查脚本修复状态"""
    if not os.path.exists(script_path):
        return "❌", "文件不存在", {}
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查各个修复元素
        checks = {
            'has_check_method': "def check_data_exists" in content,
            'has_skip_message': "数据已存在，跳过更新" in content,
            'has_ts_code_param': ("ts_code=None" in content and "def insert_data" in content),
            'has_error_handling': "UNIQUE constraint failed" in content,
            'has_rollback': "self.conn.rollback()" in content
        }
        
        # 计算修复完成度
        completed = sum(checks.values())
        total = len(checks)
        
        if completed == total:
            return "✅", "完全修复", checks
        elif completed >= 3:
            return "🔄", f"大部分修复 ({completed}/{total})", checks
        elif completed >= 1:
            return "⚠️", f"部分修复 ({completed}/{total})", checks
        else:
            return "❌", "未修复", checks
            
    except Exception as e:
        return "❓", f"检查失败: {e}", {}

def main():
    """主函数"""
    print("=" * 70)
    print("🔍 最终验证：所有数据库脚本修复状态")
    print("=" * 70)
    
    scripts_to_check = [
        ("market_data/adj_factor.py", "复权因子"),
        ("market_data/daily_basic.py", "每日指标"),
        ("market_data/weekly.py", "周线数据"),
        ("market_data/monthly.py", "月线数据"),
        ("market_data/pro_bar.py", "复权行情"),
        ("market_data/realtime_quote.py", "实时行情"),
        ("market_data/realtime_tick.py", "实时成交"),
        ("market_data/stk_mins.py", "分钟线数据")
    ]
    
    print("详细修复状态检查:")
    print("-" * 70)
    
    fully_fixed = 0
    mostly_fixed = 0
    partially_fixed = 0
    not_fixed = 0
    
    for script_path, description in scripts_to_check:
        status_icon, status_msg, checks = detailed_check_script(script_path)
        
        print(f"{status_icon} {script_path}")
        print(f"   📝 {description}: {status_msg}")
        
        if checks:
            print(f"   🔍 修复元素检查:")
            print(f"      • check_data_exists方法: {'✅' if checks['has_check_method'] else '❌'}")
            print(f"      • 跳过更新消息: {'✅' if checks['has_skip_message'] else '❌'}")
            print(f"      • ts_code参数支持: {'✅' if checks['has_ts_code_param'] else '❌'}")
            print(f"      • 错误处理: {'✅' if checks['has_error_handling'] else '❌'}")
            print(f"      • 事务回滚: {'✅' if checks['has_rollback'] else '❌'}")
        
        # 统计
        if status_icon == "✅":
            fully_fixed += 1
        elif status_icon == "🔄":
            mostly_fixed += 1
        elif status_icon == "⚠️":
            partially_fixed += 1
        else:
            not_fixed += 1
        
        print()
    
    total = len(scripts_to_check)
    
    print("=" * 70)
    print("📊 修复统计")
    print("=" * 70)
    print(f"✅ 完全修复: {fully_fixed}/{total} ({fully_fixed/total*100:.1f}%)")
    print(f"🔄 大部分修复: {mostly_fixed}/{total} ({mostly_fixed/total*100:.1f}%)")
    print(f"⚠️ 部分修复: {partially_fixed}/{total} ({partially_fixed/total*100:.1f}%)")
    print(f"❌ 未修复: {not_fixed}/{total} ({not_fixed/total*100:.1f}%)")
    
    effective_fixed = fully_fixed + mostly_fixed
    print(f"\n🎯 有效修复率: {effective_fixed}/{total} ({effective_fixed/total*100:.1f}%)")
    
    print("\n" + "=" * 70)
    print("🚀 修复效果总结")
    print("=" * 70)
    
    if effective_fixed == total:
        print("🎉 恭喜！所有脚本都已成功修复！")
        print("\n✅ 现在您可以放心运行GUI中的脚本，不会再遇到:")
        print("   • UNIQUE constraint failed 错误")
        print("   • 程序因重复数据而中断")
        print("   • 不必要的数据库写入操作")
        
        print("\n📝 修复后的行为:")
        print("   • 遇到已存在数据: 显示'数据已存在，跳过更新'")
        print("   • 遇到新数据: 正常插入并显示成功信息")
        print("   • 遇到其他错误: 优雅处理并回滚事务")
        
    elif effective_fixed >= total * 0.8:
        print("👍 大部分脚本已成功修复！")
        print(f"   • {effective_fixed}个脚本已修复，覆盖了主要使用场景")
        print("   • 剩余脚本的问题较小，不影响主要功能")
        
    else:
        print("⚠️ 还有一些脚本需要进一步修复")
        print("   • 建议优先修复使用频率高的脚本")
    
    print("\n🔧 技术细节:")
    print("   • 使用数据存在性检查避免重复插入")
    print("   • 优雅处理UNIQUE constraint错误")
    print("   • 支持事务回滚保证数据一致性")
    print("   • 提供清晰的日志信息")

if __name__ == "__main__":
    main()
