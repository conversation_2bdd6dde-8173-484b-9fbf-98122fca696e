# 股票数据获取GUI管理面板

## 概述

这是一个基于tkinter的图形用户界面，用于管理和并行运行股票数据获取脚本。该GUI提供了直观的界面来选择、执行和监控各种数据获取任务。

## 功能特性

### 🎯 核心功能
- **脚本管理**: 自动发现和管理项目中的所有数据获取脚本
- **并行执行**: 支持多线程并行运行多个脚本，提高效率
- **实时监控**: 实时显示每个脚本的运行状态和进度
- **日志记录**: 详细的运行日志，支持不同级别的消息显示
- **数据库管理**: 数据库连接状态检查和基本信息显示

### 🔧 技术特性
- **模块化设计**: 分离的脚本管理、数据库管理和线程管理模块
- **错误处理**: 完善的异常捕获和错误报告机制
- **配置管理**: 可配置的GUI设置和执行参数
- **线程安全**: 安全的多线程消息传递机制

## 文件结构

```
gui/
├── __init__.py              # 包初始化文件
├── app.py                   # 主应用启动文件
├── main_panel.py            # 主GUI面板
├── script_manager.py        # 脚本管理器
├── database_manager.py      # 数据库管理器
├── thread_manager.py        # 线程管理器
├── config.py               # 配置管理
├── gui_config.json         # GUI配置文件（自动生成）
└── README.md               # 本文件
```

## 快速开始

### 1. 启动GUI

在项目根目录运行：

```bash
python start_gui.py
```

或者直接运行GUI应用：

```bash
python gui/app.py
```

### 2. 界面说明

#### 控制面板区域
- **数据库状态**: 显示数据库连接状态
- **最大并发数**: 设置同时运行的最大脚本数量（1-10）
- **总体进度**: 显示当前执行进度
- **控制按钮**: 运行选中脚本、运行全部脚本、停止所有任务等

#### 脚本列表区域
- **模块分组**: 按数据类型分组显示脚本
- **选择功能**: 点击复选框选择要运行的脚本
- **状态显示**: 实时显示每个脚本的运行状态
- **进度信息**: 显示脚本执行进度和最后运行时间

#### 日志区域
- **实时日志**: 显示详细的运行日志
- **颜色标识**: 不同颜色表示不同级别的消息
  - 黑色: 普通信息
  - 绿色: 成功消息
  - 红色: 错误消息
  - 橙色: 警告消息

### 3. 基本操作

#### 运行单个或多个脚本
1. 在脚本列表中点击复选框选择要运行的脚本
2. 点击"运行选中脚本"按钮
3. 观察日志区域的执行情况

#### 运行所有脚本
1. 点击"运行全部脚本"按钮
2. 系统将自动运行所有可用的脚本

#### 停止任务
1. 点击"停止所有任务"按钮
2. 确认停止操作

#### 调整并发数
1. 在"最大并发数"输入框中设置值（1-10）
2. 设置会在下次运行时生效

## 配置说明

### GUI配置文件

GUI会自动创建`gui_config.json`配置文件，包含以下设置：

```json
{
  "window": {
    "width": 1400,
    "height": 900,
    "x": 100,
    "y": 100
  },
  "execution": {
    "max_workers": 4,
    "auto_refresh": true,
    "auto_check_db": true
  },
  "logging": {
    "max_lines": 1000,
    "auto_scroll": true,
    "show_timestamps": true
  },
  "database": {
    "path": "data.db",
    "auto_backup": false,
    "backup_interval": 24
  },
  "ui": {
    "theme": "default",
    "font_size": 9,
    "show_tooltips": true
  }
}
```

### 配置项说明

- **window**: 窗口大小和位置设置
- **execution**: 执行相关设置
- **logging**: 日志显示设置
- **database**: 数据库相关设置
- **ui**: 界面外观设置

## 支持的脚本模块

GUI自动识别以下模块中的脚本：

### 基础数据 (basic_data)
- 股票基本信息、交易日历、公司信息等

### 市场数据 (market_data)
- 日线、周线、月线行情数据等

### 财务数据 (financial_data)
- 资产负债表、利润表、现金流量表等

### 资金流向数据 (moneyflow_data)
- 个股资金流向、大单资金、北向资金等

### 参考数据 (reference_data)
- 概念股分类、大宗交易、股权质押等

### 特色数据 (special_data)
- 股票因子、筹码分布、机构调研等

### 大盘主题数据 (dapan_topic_data)
- 行业指数、概念指数、龙虎榜等

## 故障排除

### 常见问题

1. **GUI无法启动**
   - 检查Python环境和依赖包
   - 确保tkinter已正确安装
   - 查看控制台错误信息

2. **脚本执行失败**
   - 检查Tushare token配置
   - 确认网络连接正常
   - 查看日志区域的详细错误信息

3. **数据库连接失败**
   - 检查data.db文件是否存在
   - 确认文件权限正常
   - 尝试手动创建数据库文件

4. **界面响应缓慢**
   - 减少最大并发数
   - 清除日志内容
   - 重启GUI应用

### 日志文件

GUI运行时会产生以下日志：
- 界面日志: 显示在GUI的日志区域
- 脚本日志: 各个数据获取脚本的执行日志

## 开发说明

### 扩展新功能

1. **添加新的脚本支持**
   - 在`script_manager.py`中添加脚本描述
   - 确保脚本有`main()`函数

2. **修改界面布局**
   - 编辑`main_panel.py`中的界面创建方法
   - 调整控件布局和样式

3. **增加配置选项**
   - 在`config.py`中添加新的配置项
   - 在界面中添加相应的设置控件

### 代码结构

- **main_panel.py**: 主界面逻辑
- **script_manager.py**: 脚本发现和管理
- **database_manager.py**: 数据库操作
- **thread_manager.py**: 多线程执行管理
- **config.py**: 配置文件管理

## 许可证

本项目遵循项目主许可证。
