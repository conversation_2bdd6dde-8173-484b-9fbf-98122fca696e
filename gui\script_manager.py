import os
import sys
from pathlib import Path

class ScriptManager:
    """脚本管理器，负责发现和管理所有数据获取脚本"""
    
    def __init__(self):
        # 获取项目根目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        self.root_dir = os.path.dirname(current_dir)
        
        # 定义数据模块目录
        self.data_modules = {
            'basic_data': '基础数据',
            'market_data': '市场数据', 
            'financial_data': '财务数据',
            'moneyflow_data': '资金流向数据',
            'reference_data': '参考数据',
            'special_data': '特色数据',
            'dapan_topic_data': '大盘主题数据'
        }
        
        # 脚本描述映射
        self.script_descriptions = {
            # basic_data
            'stock_data.py': '股票基本信息',
            'trade_cal.py': '交易日历',
            'stock_company.py': '上市公司信息',
            'hs_const.py': '沪深股通成分股',
            'namechange.py': '股票更名记录',
            'stk_managers.py': '管理层信息',
            'stk_rewards.py': '管理层薪酬',
            'stk_premarket.py': '盘前数据',
            'bak_basic.py': '备用基础数据',
            
            # market_data
            'daily.py': '日线行情',
            'weekly.py': '周线行情',
            'monthly.py': '月线行情',
            'stk_daily.py': '股票日线数据',
            'daily_basic.py': '每日基本面指标',
            'adj_factor.py': '复权因子',
            'suspend_d.py': '停复牌信息',
            'stk_limit.py': '涨跌停数据',
            'stk_mins.py': '分钟线数据',
            'stk_weekly_monthly.py': '周月线数据',
            'realtime_quote.py': '实时行情',
            'realtime_tick.py': '实时成交',
            'realtime_list.py': '实时列表',
            'rt_k.py': '实时K线',
            'pro_bar.py': '复权行情',
            'bak_daily.py': '备用日线数据',
            
            # financial_data
            'balancesheet_vip.py': '资产负债表',
            'income_vip.py': '利润表',
            'cashflow_vip.py': '现金流量表',
            'fina_indicator_vip.py': '财务指标',
            'fina_audit.py': '财务审计意见',
            'fina_mainbz_vip.py': '主营业务构成',
            'express_vip.py': '业绩快报',
            'forecast_vip.py': '业绩预告',
            'dividend.py': '分红送股',
            'disclosure_date.py': '财报披露日期',
            'top10_holders.py': '前十大股东',
            'top10_floatholders.py': '前十大流通股东',
            
            # moneyflow_data
            'moneyflow.py': '个股资金流向',
            'moneyflow_hsgt.py': '沪深港通资金流向',
            'moneyflow_ths.py': '同花顺板块资金流向',
            'moneyflow_cnt_ths.py': '同花顺概念资金流向',
            'moneyflow_ind_ths.py': '同花顺行业资金流向',
            'moneyflow_dc.py': '大单资金流向',
            'moneyflow_mkt_dc.py': '市场资金流向',
            'moneyflow_ind_dc.py': '行业资金流向',
            
            # reference_data
            'block_trade.py': '大宗交易',
            'concept.py': '概念股分类',
            'pledge_stat.py': '股权质押统计',
            'pledge_detail.py': '股权质押明细',
            'repurchase.py': '股票回购',
            'share_float.py': '限售股解禁',
            'stk_holdernumber.py': '股东人数',
            'stk_holdertrade.py': '股东增减持',
            'ths_member.py': '同花顺板块成分股',
            
            # special_data
            'stk_factor.py': '股票因子',
            'stk_factor_pro.py': '专业版因子',
            'broker_recommend.py': '券商推荐',
            'cyq_chips.py': '筹码分布',
            'cyq_perf.py': '超预期统计',
            'hk_hold.py': '港股通持股',
            'ccass_hold.py': '中央结算系统持股',
            'stk_surv.py': '机构调研',
            'stk_nineturn.py': '九转序列',
            'report_rc.py': '券商研报',
            
            # dapan_topic_data
            'ths_index.py': '同花顺指数',
            'ths_daily.py': '同花顺指数日线',
            'ths_member.py': '同花顺指数成分股',
            'dc_index.py': '大成指数',
            'dc_member.py': '大成指数成分股',
            'kpl_concept.py': '卡普兰概念指数',
            'kpl_concept_cons.py': '卡普兰概念指数成分股',
            'kpl_list.py': '卡普兰指数列表',
            'limit_list_d.py': '每日涨跌停',
            'limit_list_ths.py': '同花顺涨跌停板',
            'limit_step.py': '涨跌幅梯队',
            'top_list.py': '龙虎榜',
            'top_inst.py': '机构席位龙虎榜',
            'hm_list.py': '哈密达数据',
            'stk_auction.py': '股票竞价'
        }
        
    def get_all_scripts(self):
        """获取所有可用的数据获取脚本"""
        scripts = []

        for module_dir, module_name in self.data_modules.items():
            module_path = os.path.join(self.root_dir, module_dir)

            if os.path.exists(module_path):
                # 扫描模块目录中的Python文件
                for filename in os.listdir(module_path):
                    if filename.endswith('.py') and not filename.startswith('__'):
                        script_path = os.path.join(module_path, filename)

                        # 检查文件是否有main函数
                        if self._has_main_function(script_path):
                            description = self.script_descriptions.get(filename, filename[:-3])

                            scripts.append({
                                'name': f"{description} ({filename})",
                                'filename': filename,
                                'module': module_name,
                                'module_dir': module_dir,
                                'path': script_path,
                                'relative_path': f"{module_dir}/{filename}"
                            })

        # 按模块和文件名排序
        scripts.sort(key=lambda x: (x['module'], x['filename']))
        return scripts
    
    def _has_main_function(self, script_path):
        """检查脚本是否有main函数"""
        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # 简单检查是否包含main函数定义
                return 'def main(' in content or 'def main():' in content
        except Exception:
            return False
    
    def get_script_info(self, script_path):
        """获取脚本的详细信息"""
        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 提取文档字符串
            docstring = self._extract_docstring(content)
            
            # 检查依赖
            dependencies = self._extract_dependencies(content)
            
            return {
                'docstring': docstring,
                'dependencies': dependencies,
                'has_main': 'def main(' in content or 'def main():' in content,
                'size': len(content),
                'lines': content.count('\n') + 1
            }
        except Exception as e:
            return {
                'error': str(e),
                'docstring': '',
                'dependencies': [],
                'has_main': False,
                'size': 0,
                'lines': 0
            }
    
    def _extract_docstring(self, content):
        """提取模块文档字符串"""
        lines = content.split('\n')
        docstring = ""
        in_docstring = False
        quote_type = None
        
        for line in lines:
            stripped = line.strip()
            if not in_docstring:
                if stripped.startswith('"""') or stripped.startswith("'''"):
                    quote_type = stripped[:3]
                    in_docstring = True
                    if stripped.endswith(quote_type) and len(stripped) > 3:
                        # 单行文档字符串
                        docstring = stripped[3:-3].strip()
                        break
                    else:
                        docstring = stripped[3:] + "\n"
            else:
                if stripped.endswith(quote_type):
                    docstring += line.rstrip()[:-3]
                    break
                else:
                    docstring += line + "\n"
        
        return docstring.strip()
    
    def _extract_dependencies(self, content):
        """提取脚本依赖"""
        dependencies = []
        lines = content.split('\n')
        
        for line in lines:
            stripped = line.strip()
            if stripped.startswith('import ') or stripped.startswith('from '):
                dependencies.append(stripped)
        
        return dependencies
    
    def validate_script(self, script_path):
        """验证脚本是否可以运行"""
        try:
            # 检查文件是否存在
            if not os.path.exists(script_path):
                return False, "文件不存在"
            
            # 检查是否有main函数
            if not self._has_main_function(script_path):
                return False, "缺少main函数"
            
            # 可以添加更多验证逻辑，比如语法检查等
            
            return True, "验证通过"
        except Exception as e:
            return False, f"验证失败: {str(e)}"
