#!/usr/bin/env python3
"""
批量修复数据库脚本，解决UNIQUE constraint错误
"""

import os
import re

def get_scripts_to_fix():
    """获取需要修复的脚本列表"""
    scripts_to_fix = [
        {
            'path': 'market_data/weekly.py',
            'table_name': 'weekly',
            'primary_keys': ['ts_code', 'trade_date']
        },
        {
            'path': 'market_data/monthly.py', 
            'table_name': 'monthly',
            'primary_keys': ['ts_code', 'trade_date']
        },
        {
            'path': 'market_data/pro_bar.py',
            'table_name': 'pro_bar',
            'primary_keys': ['ts_code', 'trade_date']
        },
        {
            'path': 'market_data/realtime_quote.py',
            'table_name': 'realtime_quote',
            'primary_keys': ['ts_code']
        },
        {
            'path': 'market_data/realtime_tick.py',
            'table_name': 'realtime_tick',
            'primary_keys': ['ts_code', 'trade_date']
        },
        {
            'path': 'market_data/stk_mins.py',
            'table_name': 'stk_mins',
            'primary_keys': ['ts_code', 'trade_date']
        }
    ]
    return scripts_to_fix

def generate_check_method(table_name, primary_keys):
    """生成检查数据是否存在的方法"""
    if len(primary_keys) == 2 and 'trade_date' in primary_keys:
        # 对于有trade_date的表，使用日期范围检查
        return f'''    def check_data_exists(self, ts_code, start_date, end_date):
        """检查指定股票和时间范围的数据是否已存在"""
        try:
            cursor = self.conn.cursor()
            cursor.execute(\'\'\'
                SELECT COUNT(*) FROM {table_name} 
                WHERE ts_code = ? AND trade_date >= ? AND trade_date <= ?
            \'\'\', (ts_code, start_date, end_date))
            count = cursor.fetchone()[0]
            return count > 0
        except Exception as e:
            print(f"检查数据是否存在时出错: {{e}}")
            return False'''
    else:
        # 对于其他表，使用简单的存在性检查
        return f'''    def check_data_exists(self, ts_code):
        """检查指定股票的数据是否已存在"""
        try:
            cursor = self.conn.cursor()
            cursor.execute(\'\'\'
                SELECT COUNT(*) FROM {table_name} 
                WHERE ts_code = ?
            \'\'\', (ts_code,))
            count = cursor.fetchone()[0]
            return count > 0
        except Exception as e:
            print(f"检查数据是否存在时出错: {{e}}")
            return False'''

def generate_insert_method(table_name, primary_keys):
    """生成修复后的插入方法"""
    if len(primary_keys) == 2 and 'trade_date' in primary_keys:
        # 对于有trade_date的表
        return f'''    def insert_data(self, df, ts_code=None):
        """将DataFrame数据插入到表格中"""
        try:
            if df is None or df.empty:
                print("没有数据需要写入")
                return
            
            # 如果提供了ts_code，检查数据是否已存在
            if ts_code:
                # 获取数据的时间范围
                if 'trade_date' in df.columns:
                    min_date = df['trade_date'].min()
                    max_date = df['trade_date'].max()
                    
                    if self.check_data_exists(ts_code, min_date, max_date):
                        print(f"数据已存在: {{ts_code}} ({{min_date}} 到 {{max_date}})，跳过更新")
                        return
            
            # 尝试插入数据
            df.to_sql('{table_name}', self.conn, if_exists='append', index=False)
            self.conn.commit()
            print(f"成功写入 {{len(df)}} 条记录")
            
        except Exception as e:
            error_msg = str(e)
            if "UNIQUE constraint failed" in error_msg:
                print(f"数据已存在，跳过更新: {{error_msg}}")
            else:
                print(f"写入数据时出错: {{e}}")
                self.conn.rollback()'''
    else:
        # 对于其他表
        return f'''    def insert_data(self, df, ts_code=None):
        """将DataFrame数据插入到表格中"""
        try:
            if df is None or df.empty:
                print("没有数据需要写入")
                return
            
            # 如果提供了ts_code，检查数据是否已存在
            if ts_code and self.check_data_exists(ts_code):
                print(f"数据已存在: {{ts_code}}，跳过更新")
                return
            
            # 尝试插入数据
            df.to_sql('{table_name}', self.conn, if_exists='append', index=False)
            self.conn.commit()
            print(f"成功写入 {{len(df)}} 条记录")
            
        except Exception as e:
            error_msg = str(e)
            if "UNIQUE constraint failed" in error_msg:
                print(f"数据已存在，跳过更新: {{error_msg}}")
            else:
                print(f"写入数据时出错: {{e}}")
                self.conn.rollback()'''

def check_script_status(script_path):
    """检查脚本是否需要修复"""
    if not os.path.exists(script_path):
        return False, "文件不存在"
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经修复
        if "check_data_exists" in content and "数据已存在，跳过更新" in content:
            return False, "已修复"
        
        # 检查是否使用简单的append模式
        if "to_sql(" in content and "if_exists='append'" in content:
            return True, "需要修复"
        
        return False, "不需要修复"
        
    except Exception as e:
        return False, f"检查失败: {e}"

def main():
    """主函数"""
    print("=" * 60)
    print("批量修复数据库脚本工具")
    print("=" * 60)
    
    scripts = get_scripts_to_fix()
    
    print("检查脚本状态:")
    print("-" * 40)
    
    for script in scripts:
        path = script['path']
        table_name = script['table_name']
        
        needs_fix, status = check_script_status(path)
        status_icon = "❌" if needs_fix else "✅"
        
        print(f"{status_icon} {path} ({table_name}): {status}")
        
        if needs_fix:
            print(f"   建议修复方案:")
            print(f"   1. 添加 check_data_exists 方法")
            print(f"   2. 修改 insert_data 方法")
            print(f"   3. 在调用时传入 ts_code 参数")
    
    print("\n" + "=" * 60)
    print("修复状态总结")
    print("=" * 60)
    print("✅ 已修复:")
    print("   - market_data/adj_factor.py")
    print("   - market_data/daily_basic.py")
    
    print("\n❌ 需要修复的脚本:")
    for script in scripts:
        needs_fix, _ = check_script_status(script['path'])
        if needs_fix:
            print(f"   - {script['path']} (表: {script['table_name']})")
    
    print("\n💡 修复建议:")
    print("1. 使用相同的模式修复所有脚本")
    print("2. 添加数据存在性检查")
    print("3. 优雅处理UNIQUE constraint错误")
    print("4. 减少不必要的数据库操作")
    
    print("\n🎯 预期效果:")
    print("- 避免: UNIQUE constraint failed 错误")
    print("- 显示: 数据已存在，跳过更新")
    print("- 提高: 脚本运行效率")

if __name__ == "__main__":
    main()
