import os
import sys
import time
from datetime import datetime, timedelta
import pandas as pd
import tushare as ts
import sqlite3
from pathlib import Path

# 获取项目根目录
root_dir = str(Path(__file__).resolve().parent.parent)
sys.path.insert(0, root_dir)

from config import get_token

# 初始化pro接口
pro = ts.pro_api(get_token())

class TopListDatabase:
    def __init__(self, db_name='data.db'):
        """初始化数据库连接"""
        try:
            # 使用项目根目录的数据库路径
            self.db_path = os.path.join(root_dir, db_name)
            self.conn = sqlite3.connect(self.db_path)
            print(f"成功连接到数据库: {self.db_path}")
        except sqlite3.Error as e:
            print(f"数据库连接错误: {e}")
            raise

    def create_table(self):
        """创建龙虎榜每日明细表"""
        try:
            create_table_sql = '''CREATE TABLE IF NOT EXISTS top_list (
                trade_date TEXT,
                ts_code TEXT,
                name TEXT,
                close FLOAT,
                pct_change FLOAT,
                turnover_rate FLOAT,
                amount FLOAT,
                l_sell FLOAT,
                l_buy FLOAT,
                l_amount FLOAT,
                net_amount FLOAT,
                net_rate FLOAT,
                amount_rate FLOAT,
                float_values FLOAT,
                reason TEXT,
                PRIMARY KEY (trade_date, ts_code)
            )'''
            
            # 首先删除已存在的表
            self.conn.execute('DROP TABLE IF EXISTS top_list')
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 top_list 创建成功")
            
        except Exception as e:
            print(f"创建表格 top_list 时出错: {e}")
            raise

    def insert_data(self, df):
        """将DataFrame数据插入到表格中"""
        try:
            if df is None or df.empty:
                return
                
            # 创建临时表
            temp_table_name = 'temp_top_list'
            df.to_sql(temp_table_name, self.conn, if_exists='replace', index=False)
            
            # 使用INSERT OR REPLACE将数据从临时表插入到主表
            insert_sql = f'''
                INSERT OR REPLACE INTO top_list 
                SELECT * FROM {temp_table_name}
            '''
            self.conn.execute(insert_sql)
            
            # 删除临时表
            self.conn.execute(f'DROP TABLE IF EXISTS {temp_table_name}')
            
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")
        except Exception as e:
            print(f"写入数据时出错: {e}")
            self.conn.rollback()

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_date_ranges():
    """获取最近5年的交易日历"""
    try:
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=3*365)).strftime('%Y%m%d')
        
        # 获取交易日历
        cal_df = pro.trade_cal(start_date=start_date, end_date=end_date, is_open=1)
        if cal_df.empty:
            print("未能获取交易日历数据")
            return []
            
        # 按月份分组
        cal_df['month'] = cal_df['cal_date'].str[:6]
        date_ranges = []
        
        for month in cal_df['month'].unique():
            month_dates = cal_df[cal_df['month'] == month]['cal_date']
            if not month_dates.empty:
                date_ranges.append({
                    'start_date': month_dates.iloc[0],
                    'end_date': month_dates.iloc[-1]
                })
        
        return date_ranges
    except Exception as e:
        print(f"获取日期范围时出错: {e}")
        return []

def fetch_top_list_data(trade_date):
    """获取指定日期的龙虎榜数据"""
    try:
        df = pro.top_list(trade_date=trade_date)
        if df is None or df.empty:
            print(f"当日无龙虎榜数据: {trade_date}")
            return None
            
        return df
    except Exception as e:
        print(f"获取龙虎榜数据时出错 ({trade_date}): {e}")
        if "抱歉，您每分钟最多访问该接口10次" in str(e):
            print("触发频率限制，等待60秒...")
            time.sleep(60)
        return None

def process_date_range(db, start_date, end_date):
    """处理指定日期范围的数据"""
    try:
        # 获取该范围内的交易日
        trading_dates = pro.trade_cal(start_date=start_date, 
                                    end_date=end_date,
                                    is_open=1)['cal_date'].tolist()
        
        for trade_date in trading_dates:
            try:
                df = fetch_top_list_data(trade_date)
                if df is not None and not df.empty:
                    db.insert_data(df)
                    print(f"成功获取并保存 {trade_date} 的龙虎榜数据")
                time.sleep(0.5)  # 添加延时以避免触发频率限制
            except Exception as e:
                print(f"处理日期 {trade_date} 时出错: {e}")
                continue
                
    except Exception as e:
        print(f"处理日期范围时出错 ({start_date} to {end_date}): {e}")

def main():
    # 确保数据目录存在
    os.makedirs('dapan_topic_data', exist_ok=True)
    
    # 创建数据库实例
    db = TopListDatabase()
    
    try:
        # 创建表
        db.create_table()
        
        # 获取日期范围
        date_ranges = get_date_ranges()
        if not date_ranges:
            print("未能获取有效的日期范围，程序退出")
            return
            
        print(f"将获取 {len(date_ranges)} 个月份的数据")
        
        # 处理所有日期范围的数据
        for date_range in date_ranges:
            print(f"处理日期范围: {date_range['start_date']} 到 {date_range['end_date']}")
            process_date_range(db, date_range['start_date'], date_range['end_date'])
            
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行龙虎榜每日明细数据获取程序...")
    main()
    print("程序运行完成！") 