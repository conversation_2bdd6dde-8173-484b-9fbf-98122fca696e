#!/usr/bin/env python3
"""
股票数据获取GUI管理面板启动脚本
放置在项目根目录，方便启动GUI界面
"""

import os
import sys

def main():
    """启动GUI管理面板"""
    # 获取当前脚本所在目录（项目根目录）
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # GUI应用路径
    gui_app_path = os.path.join(current_dir, 'gui', 'app.py')
    
    # 检查GUI应用是否存在
    if not os.path.exists(gui_app_path):
        print("错误: 找不到GUI应用文件")
        print(f"期望路径: {gui_app_path}")
        print("请确保gui文件夹和相关文件存在")
        return 1
    
    # 添加当前目录到Python路径
    sys.path.insert(0, current_dir)
    
    try:
        # 导入并运行GUI应用
        print("正在启动股票数据获取GUI管理面板...")
        
        # 切换到GUI目录
        gui_dir = os.path.join(current_dir, 'gui')
        os.chdir(gui_dir)
        
        # 执行GUI应用
        exec(open(gui_app_path).read())
        
    except Exception as e:
        print(f"启动GUI时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    print("=" * 50)
    print("股票数据获取GUI管理面板")
    print("=" * 50)
    
    exit_code = main()
    
    if exit_code != 0:
        input("按回车键退出...")
    
    sys.exit(exit_code)
