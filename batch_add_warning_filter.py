#!/usr/bin/env python3
"""
批量为数据获取脚本添加警告过滤器
"""

import os
import glob
import re

def find_python_scripts():
    """查找所有Python数据获取脚本"""
    script_patterns = [
        'market_data/*.py',
        'basic_data/*.py', 
        'financial_data/*.py',
        'moneyflow_data/*.py',
        'dapan_topic_data/*.py',
        'fetchers/*.py'
    ]
    
    all_scripts = []
    for pattern in script_patterns:
        scripts = glob.glob(pattern)
        all_scripts.extend(scripts)
    
    # 过滤掉__init__.py和测试文件
    filtered_scripts = []
    for script in all_scripts:
        if not script.endswith('__init__.py') and 'test' not in script.lower():
            filtered_scripts.append(script)
    
    return filtered_scripts

def check_has_warning_filter(file_path):
    """检查文件是否已经有警告过滤器"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            return 'warnings.filterwarnings' in content or 'FutureWarning' in content
    except:
        return False

def add_warning_filter(file_path):
    """为脚本添加警告过滤器"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找导入语句的位置
        import_end_index = 0
        for i, line in enumerate(lines):
            if line.strip().startswith('import ') or line.strip().startswith('from '):
                import_end_index = i + 1
            elif line.strip() and not line.strip().startswith('#'):
                break
        
        # 准备要插入的警告过滤代码
        warning_code = [
            "import warnings\n",
            "\n",
            "# 过滤tushare的FutureWarning警告\n",
            "warnings.filterwarnings('ignore', category=FutureWarning, module='tushare.*')\n",
            "warnings.filterwarnings('ignore', message=\".*fillna with 'method' is deprecated.*\")\n",
            "\n"
        ]
        
        # 检查是否已经导入了warnings
        has_warnings_import = any('import warnings' in line for line in lines)
        if has_warnings_import:
            # 如果已经导入了warnings，只添加过滤器
            warning_code = warning_code[2:]  # 跳过import warnings
        
        # 插入警告过滤代码
        lines = lines[:import_end_index] + warning_code + lines[import_end_index:]
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        return True
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False

def add_simple_import_method(file_path):
    """使用简单的导入方法添加警告过滤"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 在第一个import语句前添加警告配置导入
        import_pattern = r'^(import |from )'
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            if re.match(import_pattern, line.strip()):
                # 在第一个import前插入
                lines.insert(i, '# 导入警告配置')
                lines.insert(i+1, 'import warning_config  # 自动配置警告过滤器')
                lines.insert(i+2, '')
                break
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        return True
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    print("🔧 批量添加警告过滤器")
    print("=" * 40)
    
    # 查找所有Python脚本
    scripts = find_python_scripts()
    print(f"找到 {len(scripts)} 个Python脚本")
    
    if not scripts:
        print("没有找到任何Python脚本")
        return
    
    print("\n选择处理方式:")
    print("1. 直接添加警告过滤代码")
    print("2. 添加warning_config模块导入")
    print("3. 只显示需要处理的文件")
    print("4. 退出")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == '3':
        print("\n需要处理的文件:")
        for script in scripts:
            has_filter = check_has_warning_filter(script)
            status = "✅ 已有过滤器" if has_filter else "❌ 需要添加"
            print(f"  {script:<40} {status}")
        return
    
    if choice == '4':
        print("退出程序")
        return
    
    # 处理文件
    processed = 0
    skipped = 0
    failed = 0
    
    for script in scripts:
        print(f"\n处理: {script}")
        
        # 检查是否已经有警告过滤器
        if check_has_warning_filter(script):
            print(f"  ⏭️ 跳过 - 已有警告过滤器")
            skipped += 1
            continue
        
        # 根据选择的方式处理
        if choice == '1':
            success = add_warning_filter(script)
        elif choice == '2':
            success = add_simple_import_method(script)
        else:
            print("无效选择")
            return
        
        if success:
            print(f"  ✅ 成功添加警告过滤器")
            processed += 1
        else:
            print(f"  ❌ 添加失败")
            failed += 1
    
    print(f"\n📊 处理结果:")
    print(f"  成功处理: {processed}")
    print(f"  跳过文件: {skipped}")
    print(f"  处理失败: {failed}")
    print(f"  总计文件: {len(scripts)}")

if __name__ == "__main__":
    main()
