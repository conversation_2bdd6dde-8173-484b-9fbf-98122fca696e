import os
import sys
import time
import pandas as pd
import sqlite3
import tushare as ts
from datetime import datetime, timedelta

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

from config import get_token

# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

class StockDatabase:
    def __init__(self, db_path=None):
        """初始化数据库连接"""
        if db_path is None:
            # 使用项目根目录下的data.db
            db_path = os.path.join(root_dir, 'data.db')
        self.db_name = db_path
        self.conn = sqlite3.connect(db_path)
        print(f"数据库 {db_path} 连接成功")

    def create_table(self):
        """创建神奇九转指标表"""
        try:
            create_table_sql = '''CREATE TABLE IF NOT EXISTS stk_nineturn (
                ts_code TEXT,
                trade_date DATETIME,
                freq TEXT,
                open FLOAT,
                high FLOAT,
                low FLOAT,
                close FLOAT,
                vol FLOAT,
                amount FLOAT,
                up_count FLOAT,
                down_count FLOAT,
                nine_up_turn TEXT,
                nine_down_turn TEXT,
                PRIMARY KEY (ts_code, trade_date, freq)
            )'''
            
            # 首先删除已存在的表
            self.conn.execute('DROP TABLE IF EXISTS stk_nineturn')
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 stk_nineturn 创建成功")
            
        except Exception as e:
            print(f"创建表格 stk_nineturn 时出错: {e}")
            raise

    def insert_data(self, df):
        """将DataFrame数据插入到表格中"""
        try:
            # 创建临时表
            temp_table_name = 'temp_stk_nineturn'
            df.to_sql(temp_table_name, self.conn, if_exists='replace', index=False)
            
            # 使用INSERT OR REPLACE将数据从临时表插入到主表
            insert_sql = f'''
                INSERT OR REPLACE INTO stk_nineturn 
                SELECT * FROM {temp_table_name}
            '''
            self.conn.execute(insert_sql)
            
            # 删除临时表
            self.conn.execute(f'DROP TABLE IF EXISTS {temp_table_name}')
            
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")
        except Exception as e:
            print(f"写入数据时出错: {e}")
            self.conn.rollback()

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_stock_list():
    """获取股票列表"""
    try:
        df = pro.stock_basic(exchange='', list_status='L')
        return df['ts_code'].tolist()
    except Exception as e:
        print(f"获取股票列表时出错: {e}")
        return []

def get_date_ranges():
    """获取近5年的日期范围，按月划分"""
    ranges = []
    end_date = datetime.now()
    start_date = max(end_date - timedelta(days=3*365), datetime(2023, 1, 1))  # 从2023年1月1日开始
    
    # 将时间范围按月划分
    current_date = end_date
    while current_date > start_date:
        month_end = current_date.strftime('%Y%m%d')
        current_date = current_date - timedelta(days=30)  # 大约一个月
        month_start = current_date.strftime('%Y%m%d')
        ranges.append((month_start, month_end))
    
    return ranges

def fetch_nineturn_data(ts_code, start_date, end_date, freq):
    """获取指定股票和日期范围的神奇九转指标数据"""
    try:
        print(f"正在获取 {ts_code} 从 {start_date} 至 {end_date} 的{freq}神奇九转指标数据...")
        df = pro.stk_nineturn(ts_code=ts_code, start_date=start_date, end_date=end_date, freq=freq)
        time.sleep(0.5)  # 添加延时以避免频率限制
        return df
    except Exception as e:
        print(f"获取 {ts_code} 的神奇九转指标数据时出错: {e}")
        return None

def process_stock_data(ts_code, date_ranges, db):
    """处理单个股票的所有时期数据"""
    frequencies = ['daily', '60min']  # 处理日线和60分钟线数据
    
    for freq in frequencies:
        print(f"正在处理 {ts_code} 的 {freq} 数据...")
        for start_date, end_date in date_ranges:
            df = fetch_nineturn_data(ts_code, start_date, end_date, freq)
            if df is not None and not df.empty:
                db.insert_data(df)
                print(f"成功获取 {ts_code} 从 {start_date} 至 {end_date} 的 {freq} 数据，共 {len(df)} 条记录")
            else:
                print(f"跳过 {ts_code} 从 {start_date} 至 {end_date} 的 {freq} 数据处理")

def main():
    # 确保special_data目录存在
    os.makedirs('special_data', exist_ok=True)
    
    # 创建数据库实例
    db = StockDatabase()
    
    try:
        # 创建表
        db.create_table()
        
        # 获取股票列表
        stock_list = get_stock_list()
        if not stock_list:
            print("获取股票列表失败")
            return
        
        # 获取日期范围列表
        date_ranges = get_date_ranges()
        print(f"将获取以下日期范围的数据: {date_ranges}")
        
        # 处理每只股票
        total_stocks = len(stock_list)
        for idx, ts_code in enumerate(stock_list, 1):
            print(f"正在处理第 {idx}/{total_stocks} 只股票: {ts_code}")
            process_stock_data(ts_code, date_ranges, db)
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行神奇九转指标数据获取程序...")
    main()
    print("程序运行完成！") 