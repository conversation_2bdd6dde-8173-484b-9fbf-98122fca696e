import sys
import os
import time
import json
import urllib.parse
from datetime import datetime
from threading import Thread
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import webbrowser
from news_parser import NewsParser
from db_manager import NewsDatabase

class StockNewsCrawler:
    def __init__(self, root):
        self.root = root
        self.root.title("股票市场新闻采集工具")
        self.root.geometry("1200x800")
        
        # 新闻源配置
        self.news_sources = {
            "凤凰网财经": "https://finance.ifeng.com/",
            "新浪财经": "https://finance.sina.com.cn/stock/",
            "东方财富": "https://finance.eastmoney.com/",
            "金融界": "https://finance.jrj.com.cn/",
            "同花顺财经": "https://stock.10jqka.com.cn/",
            "网易财经": "https://money.163.com/stock/",
            "腾讯财经": "https://new.qq.com/ch/finance/"
        }
        
        # 当前选择的新闻源
        self.current_source = tk.StringVar(value=list(self.news_sources.keys())[0])
        
        # 数据库管理器
        self.db = NewsDatabase()
        
        # 新闻数据
        self.news_data = []
        
        # 调试模式
        self.debug_mode = tk.BooleanVar(value=True)
        
        # 创建GUI组件
        self.create_widgets()
        
        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def on_closing(self):
        """关闭应用程序时的处理"""
        try:
            # 关闭DrissionPage浏览器
            self.log("正在关闭浏览器...")
            NewsParser.close_page()
            self.log("浏览器已关闭")
        except Exception as e:
            self.log(f"关闭浏览器时出错: {e}")
        
        # 关闭应用程序
        self.root.destroy()
        
    def create_widgets(self):
        # 顶部框架 - 新闻源选择和控制按钮
        top_frame = ttk.Frame(self.root, padding="10")
        top_frame.pack(fill=tk.X)
        
        ttk.Label(top_frame, text="选择新闻源:").pack(side=tk.LEFT, padx=(0, 10))
        
        # 新闻源下拉菜单
        source_combo = ttk.Combobox(
            top_frame, 
            textvariable=self.current_source,
            values=list(self.news_sources.keys()),
            width=20,
            state="readonly"
        )
        source_combo.pack(side=tk.LEFT, padx=(0, 20))
        
        # 采集按钮
        ttk.Button(
            top_frame, 
            text="开始采集", 
            command=self.start_crawling
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        # 保存按钮
        ttk.Button(
            top_frame, 
            text="保存到数据库", 
            command=self.save_to_database
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        # 历史记录按钮
        ttk.Button(
            top_frame, 
            text="查看历史记录", 
            command=self.view_history
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        # 清除按钮
        ttk.Button(
            top_frame, 
            text="清除内容", 
            command=self.clear_content
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        # 打开链接按钮
        ttk.Button(
            top_frame, 
            text="打开原文链接", 
            command=self.open_news_url
        ).pack(side=tk.LEFT)
        
        # 调试模式复选框
        ttk.Checkbutton(
            top_frame,
            text="调试模式",
            variable=self.debug_mode
        ).pack(side=tk.RIGHT, padx=(10, 0))
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(top_frame, textvariable=self.status_var).pack(side=tk.RIGHT)
        
        # 搜索框架
        search_frame = ttk.Frame(self.root, padding="10")
        search_frame.pack(fill=tk.X)
        
        ttk.Label(search_frame, text="搜索关键词:").pack(side=tk.LEFT, padx=(0, 10))
        
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(
            search_frame, 
            text="搜索", 
            command=self.search_news
        ).pack(side=tk.LEFT)
        
        # 中部框架 - 新闻列表
        mid_frame = ttk.Frame(self.root, padding="10")
        mid_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建树状视图显示新闻列表
        self.tree = ttk.Treeview(mid_frame, columns=("title", "date", "source"), show="headings")
        self.tree.heading("title", text="新闻标题")
        self.tree.heading("date", text="发布日期")
        self.tree.heading("source", text="来源")
        
        self.tree.column("title", width=600)
        self.tree.column("date", width=150)
        self.tree.column("source", width=150)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(mid_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定事件 - 单击显示新闻内容
        self.tree.bind("<ButtonRelease-1>", self.show_news_content)
        
        # 底部框架 - 新闻内容和日志
        bottom_frame = ttk.Frame(self.root)
        bottom_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加一个笔记本控件，切换内容和日志视图
        self.notebook = ttk.Notebook(bottom_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 内容标签页
        content_frame = ttk.Frame(self.notebook)
        self.notebook.add(content_frame, text="新闻内容")
        
        # 文本区域显示新闻内容
        self.news_content = scrolledtext.ScrolledText(content_frame, wrap=tk.WORD, width=80, height=10)
        self.news_content.pack(fill=tk.BOTH, expand=True)
        
        # 日志标签页
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="日志")
        
        # 日志区域
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, width=80, height=10)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
    def log(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_line = f"[{timestamp}] {message}\n"
        
        # 在主线程中更新UI
        def update_log():
            self.log_text.insert(tk.END, log_line)
            self.log_text.see(tk.END)  # 滚动到最新位置
        
        self.root.after(0, update_log)
        
        if self.debug_mode.get():
            print(message)
    
    def start_crawling(self):
        """开始爬取新闻"""
        source_name = self.current_source.get()
        source_url = self.news_sources[source_name]
        
        # 更新状态
        self.status_var.set(f"正在从 {source_name} 采集新闻...")
        self.log(f"开始从 {source_name} 采集新闻，URL: {source_url}")
        self.root.update()
        
        # 清除之前的内容
        self.clear_tree()
        self.news_content.delete(1.0, tk.END)
        self.news_data = []
        
        # 切换到日志标签页，以便查看采集进度
        self.notebook.select(1)
        
        # 在后台线程中爬取，避免GUI卡死
        Thread(target=self._crawl_news, args=(source_name, source_url), daemon=True).start()
    
    def _crawl_news(self, source_name, source_url):
        """实际的爬取过程，在后台线程中运行"""
        try:
            # 使用解析器获取新闻
            self.log(f"调用解析器获取 {source_name} 的新闻")
            news_items = NewsParser.parse_news(source_name, source_url)
            
            if not news_items:
                error_msg = f"未从 {source_name} 获取到新闻数据"
                self.log(error_msg)
                # 显示一个更友好的错误对话框
                self.root.after(0, lambda: messagebox.showwarning(
                    "采集结果", 
                    f"{error_msg}\n\n可能的原因:\n"
                    f"1. 网络连接问题\n"
                    f"2. 网站结构可能已更改\n"
                    f"3. 网站可能有反爬虫措施\n\n"
                    f"请查看日志标签页了解详细信息，或尝试选择其他新闻源。"
                ))
                self._update_status(f"未找到新闻数据")
                return
            
            # 更新UI
            self.news_data = news_items
            self._update_ui_with_news()
            
            self._update_status(f"成功从 {source_name} 采集到 {len(news_items)} 条新闻")
            self.log(f"采集完成，共获取 {len(news_items)} 条新闻")
            
            # 切换到内容标签页
            self.notebook.select(0)
            
        except Exception as e:
            error_msg = f"采集出错: {str(e)}"
            self._update_status(error_msg)
            self.log(f"错误: {error_msg}")
            import traceback
            self.log(f"错误详情: {traceback.format_exc()}")
            
            # 显示错误对话框
            self.root.after(0, lambda: messagebox.showerror(
                "采集错误", 
                f"采集新闻时发生错误:\n{str(e)}\n\n请查看日志标签页了解详细信息。"
            ))
    
    def _update_ui_with_news(self):
        """更新UI显示新闻"""
        def update():
            self.clear_tree()
            for item in self.news_data:
                self.tree.insert("", "end", values=(
                    item['title'], 
                    item['date'], 
                    item['source']
                ))
        
        # 在主线程中更新UI
        self.root.after(0, update)
    
    def _update_status(self, message):
        """更新状态栏"""
        def update():
            self.status_var.set(message)
        
        # 在主线程中更新UI
        self.root.after(0, update)
    
    def show_news_content(self, event):
        """显示选中新闻的内容"""
        selected_item = self.tree.selection()
        if not selected_item:
            return
        
        try:
            # 获取选中项的索引
            item_id = self.tree.index(selected_item[0])
            if 0 <= item_id < len(self.news_data):
                news = self.news_data[item_id]
                
                # 清除现有内容
                self.news_content.delete(1.0, tk.END)
                
                # 显示新闻内容
                self.news_content.insert(tk.END, f"标题: {news['title']}\n\n")
                self.news_content.insert(tk.END, f"发布时间: {news['date']}\n\n")
                self.news_content.insert(tk.END, f"来源: {news['source']}\n\n")
                
                if news['url']:
                    self.news_content.insert(tk.END, f"链接: {news['url']}\n\n")
                
                # 如果还没有获取内容，就获取内容
                if not news['content'] and news['url']:
                    self.status_var.set(f"正在获取新闻正文...")
                    self.log(f"开始获取新闻正文: {news['url']}")
                    self.root.update()
                    Thread(target=self._fetch_content, args=(item_id, news['url']), daemon=True).start()
                else:
                    content = news['content'] if news['content'] else "无法获取正文内容，可能需要点击链接查看原文。"
                    self.news_content.insert(tk.END, "正文内容:\n\n")
                    self.news_content.insert(tk.END, content)
        except Exception as e:
            self.log(f"显示新闻内容时出错: {e}")
    
    def _fetch_content(self, news_index, url):
        """获取新闻内容"""
        try:
            self.log(f"使用DrissionPage获取新闻正文: {url}")
            content = NewsParser.extract_content(url)
            
            # 更新新闻数据
            if 0 <= news_index < len(self.news_data):
                self.news_data[news_index]['content'] = content
                
                # 更新显示
                def update_content():
                    selected_items = self.tree.selection()
                    if selected_items and self.tree.index(selected_items[0]) == news_index:
                        self.news_content.delete(1.0, tk.END)
                        
                        news = self.news_data[news_index]
                        self.news_content.insert(tk.END, f"标题: {news['title']}\n\n")
                        self.news_content.insert(tk.END, f"发布时间: {news['date']}\n\n")
                        self.news_content.insert(tk.END, f"来源: {news['source']}\n\n")
                        
                        if news['url']:
                            self.news_content.insert(tk.END, f"链接: {news['url']}\n\n")
                        
                        self.news_content.insert(tk.END, "正文内容:\n\n")
                        self.news_content.insert(tk.END, content)
                
                self.root.after(0, update_content)
                self._update_status("就绪")
                self.log(f"成功获取新闻正文，长度: {len(content)}")
        except Exception as e:
            error_msg = f"获取内容出错: {str(e)}"
            self._update_status(error_msg)
            self.log(error_msg)
    
    def open_news_url(self):
        """在浏览器中打开选中新闻的原文链接"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showinfo("提示", "请先选择一条新闻")
            return
        
        # 获取选中项的索引
        item_id = self.tree.index(selected_item[0])
        if 0 <= item_id < len(self.news_data):
            news = self.news_data[item_id]
            url = news.get('url', '')
            
            if url:
                try:
                    self.log(f"打开链接: {url}")
                    webbrowser.open(url)
                except Exception as e:
                    messagebox.showerror("错误", f"无法打开链接: {e}")
                    self.log(f"打开链接失败: {e}")
            else:
                messagebox.showinfo("提示", "该新闻没有可用的链接")
                self.log("该新闻没有可用的链接")
    
    def save_to_database(self):
        """保存当前获取的新闻到数据库"""
        if not self.news_data:
            messagebox.showinfo("提示", "没有可保存的新闻数据")
            return
        
        try:
            self.log(f"开始保存 {len(self.news_data)} 条新闻到数据库")
            success, message = self.db.save_news(self.news_data)
            
            if success:
                messagebox.showinfo("成功", message)
                self.log(f"保存成功: {message}")
            else:
                messagebox.showerror("保存失败", message)
                self.log(f"保存失败: {message}")
        except Exception as e:
            error_msg = f"保存到数据库时出错: {str(e)}"
            messagebox.showerror("保存失败", error_msg)
            self.log(error_msg)
    
    def view_history(self):
        """查看历史新闻记录"""
        try:
            source = self.current_source.get() if messagebox.askyesno("筛选", "是否只查看当前选择的新闻源的历史记录？") else None
            
            self.log(f"从数据库加载历史记录 {source if source else '所有来源'}")
            
            # 清除现有数据
            self.clear_tree()
            self.news_data = []
            
            # 从数据库获取历史记录
            news_items = self.db.get_news(limit=200, source=source)
            
            if not news_items:
                messagebox.showinfo("提示", "没有找到历史记录")
                self.log("未找到历史记录")
                return
            
            # 更新news_data和UI
            self.news_data = news_items
            self._update_ui_with_news()
            
            self._update_status(f"已加载 {len(news_items)} 条历史记录")
            self.log(f"成功加载 {len(news_items)} 条历史记录")
            
        except Exception as e:
            error_msg = f"加载历史记录出错: {str(e)}"
            self._update_status(error_msg)
            self.log(error_msg)
    
    def search_news(self):
        """搜索新闻"""
        keyword = self.search_var.get().strip()
        if not keyword:
            messagebox.showinfo("提示", "请输入搜索关键词")
            return
        
        try:
            self.log(f"搜索关键词: '{keyword}'")
            
            # 清除现有数据
            self.clear_tree()
            self.news_data = []
            
            # 从数据库搜索
            news_items = self.db.search_news(keyword)
            
            if not news_items:
                messagebox.showinfo("提示", f"没有找到包含 '{keyword}' 的新闻")
                self.log(f"未找到包含关键词 '{keyword}' 的新闻")
                return
            
            # 更新news_data和UI
            self.news_data = news_items
            self._update_ui_with_news()
            
            self._update_status(f"搜索 '{keyword}' 找到 {len(news_items)} 条结果")
            self.log(f"搜索完成，找到 {len(news_items)} 条结果")
            
        except Exception as e:
            error_msg = f"搜索出错: {str(e)}"
            self._update_status(error_msg)
            self.log(error_msg)
    
    def clear_content(self):
        """清除当前显示的内容"""
        self.clear_tree()
        self.news_content.delete(1.0, tk.END)
        self.news_data = []
        self.status_var.set("就绪")
        self.search_var.set("")
        self.log("已清除所有内容")
    
    def clear_tree(self):
        """清除树状视图的所有项"""
        for item in self.tree.get_children():
            self.tree.delete(item)

def main():
    root = tk.Tk()
    app = StockNewsCrawler(root)
    root.mainloop()

if __name__ == "__main__":
    main() 