import re
import json
import requests
from bs4 import BeautifulSoup
import urllib.parse
import time
from datetime import datetime, timedelta
try:
    # 尝试导入DrissionPage 4.0+版本
    from DrissionPage import ChromiumPage
    from DrissionPage.configs.chromium_options import ChromiumOptions
    DRISSIONPAGE_VERSION = "4.0+"
except ImportError:
    try:
        # 尝试导入DrissionPage旧版本
        from DrissionPage import ChromiumPage, ChromiumOptions
        DRISSIONPAGE_VERSION = "older"
    except ImportError:
        print("警告: DrissionPage未安装，将使用传统的requests方式")
        ChromiumPage = None
        ChromiumOptions = None
        DRISSIONPAGE_VERSION = None

class NewsParser:
    """处理不同新闻源的解析器"""
    
    # 存储页面实例，以便重用
    _page_instance = None
    
    @classmethod
    def get_page(cls):
        """获取或创建DrissionPage实例"""
        if cls._page_instance is None:
            print("初始化DrissionPage...")
            if ChromiumPage is None:
                print("DrissionPage未安装，无法使用浏览器模式")
                return None
                
            try:
                # 设置浏览器选项
                options = ChromiumOptions()
                
                # 不同版本的DrissionPage可能有不同的headless设置方法
                try:
                    # 尝试新版本的设置方法
                    options.headless(False)  # 不使用无头模式，以便观察页面加载
                except Exception as e:
                    try:
                        # 回退到旧版本的设置方法
                        options.set_headless(False)
                    except Exception as e2:
                        # 如果两种方法都不起作用，尝试使用参数设置
                        print(f"无法设置headless模式: {e}, {e2}")
                        options.set_argument("--headless=new")
                
                options.set_argument("--disable-gpu")
                options.set_argument("--no-sandbox")
                options.set_argument("--disable-dev-shm-usage")
                
                # 设置user-agent
                user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36'
                
                # 尝试不同的user-agent设置方法
                try:
                    # 尝试新版本可能的方法
                    options.user_agent(user_agent)
                except Exception as e1:
                    try:
                        # 尝试旧版本可能的方法
                        options.set_user_agent(user_agent)
                    except Exception as e2:
                        # 如果上述都失败，尝试用参数设置
                        print(f"无法设置user-agent: {e1}, {e2}")
                        options.set_argument(f"--user-agent={user_agent}")
                
                # 创建页面实例
                print(f"使用DrissionPage {DRISSIONPAGE_VERSION}版本创建浏览器")
                try:
                    # 尝试不同版本的初始化方式
                    if DRISSIONPAGE_VERSION == "4.0+":
                        cls._page_instance = ChromiumPage(options=options)  # 新版本API
                    else:
                        # 旧版本可能使用不同的参数名
                        try:
                            cls._page_instance = ChromiumPage(chromium_options=options)
                        except TypeError:
                            try:
                                cls._page_instance = ChromiumPage(options=options)
                            except TypeError:
                                # 尝试不带参数初始化，然后设置选项
                                cls._page_instance = ChromiumPage()
                                print("使用无参数初始化ChromiumPage")
                    print("DrissionPage初始化完成")
                except Exception as e:
                    print(f"初始化ChromiumPage失败: {e}")
                    import traceback
                    print(traceback.format_exc())
                    return None
            except Exception as e:
                print(f"初始化DrissionPage失败: {e}")
                import traceback
                print(traceback.format_exc())
                return None
        
        return cls._page_instance
    
    @classmethod
    def close_page(cls):
        """关闭浏览器"""
        if cls._page_instance is not None:
            try:
                cls._page_instance.quit()
                cls._page_instance = None
                print("已关闭DrissionPage浏览器")
            except Exception as e:
                print(f"关闭浏览器出错: {e}")
    
    @staticmethod
    def get_headers():
        """获取请求头"""
        return {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        }
    
    @staticmethod
    def fetch_page(url):
        """使用DrissionPage获取页面内容，如果不可用则回退到requests"""
        try:
            print(f"正在获取页面: {url}")
            
            # 获取或创建页面实例
            page = NewsParser.get_page()
            
            # 如果DrissionPage不可用，使用requests
            if page is None:
                print("DrissionPage不可用，使用requests替代")
                # 为了防止反爬，添加延迟
                time.sleep(1)
                
                # 添加更多的headers信息，模拟真实浏览器
                headers = NewsParser.get_headers()
                
                response = requests.get(url, headers=headers, timeout=20)
                print(f"页面获取状态: 状态码={response.status_code}")
                return response.text
            
            # 使用DrissionPage访问URL
            try:
                page.get(url)
                print(f"页面标题: {page.title}")
                
                # 等待页面加载完成
                time.sleep(2)  # 等待动态内容加载
                
                # 保存截图用于调试
                try:
                    screenshot_path = f"debug_screenshot_{url.split('/')[-1].split('?')[0]}.png"
                    page.save_screenshot(screenshot_path)
                    print(f"已保存页面截图到 {screenshot_path}")
                except Exception as e:
                    print(f"保存截图失败: {e}")
                
                # 获取页面HTML
                html_content = page.html
                
                print(f"页面获取成功: {url}, 内容长度: {len(html_content)}")
                return html_content
            except Exception as e:
                print(f"使用DrissionPage获取页面失败: {e}，尝试使用requests替代")
                # 回退到requests
                time.sleep(1)
                headers = NewsParser.get_headers()
                response = requests.get(url, headers=headers, timeout=20)
                print(f"使用requests获取页面: 状态码={response.status_code}")
                return response.text
                
        except Exception as e:
            print(f"获取页面失败: {url}, 错误: {e}")
            import traceback
            print(traceback.format_exc())
            return None
    
    @staticmethod
    def parse_news(source_name, source_url):
        """解析新闻内容"""
        print(f"开始解析新闻源: {source_name}, URL: {source_url}")
        html_content = NewsParser.fetch_page(source_url)
        if not html_content:
            print("获取页面内容失败，返回空列表")
            return []
        
        # 保存HTML以便调试
        try:
            debug_filename = f"debug_{source_name.replace(' ', '_')}.html"
            with open(debug_filename, "w", encoding="utf-8") as f:
                f.write(html_content)
            print(f"已保存HTML内容到{debug_filename}供调试")
        except Exception as e:
            print(f"保存HTML失败: {e}")
        
        # 使用DrissionPage直接解析元素
        try:
            # 获取页面实例
            page = NewsParser.get_page()
            
            # 如果DrissionPage可用，使用它直接解析
            if page is not None:
                news_items = []
                
                print("使用DrissionPage直接解析页面元素")
                
                # 通用解析逻辑，尝试不同的选择器
                selectors = [
                    # 新闻列表项通用选择器
                    '.news-list li', '.news-list .news-item', '.list li', '.newslist li',
                    '.content-list li', '.article-list li', '.news_item', '.news-item',
                    'ul.list li', '.feed-card-item'
                ]
                
                try:
                    for selector in selectors:
                        try:
                            elements = page.eles(selector)
                            if elements:
                                print(f"找到选择器 {selector} 的 {len(elements)} 个元素")
                                
                                for element in elements:
                                    try:
                                        # 尝试提取标题
                                        title_elem = element.ele('tag:a, tag:h2, tag:h3, tag:h4, .title') or element
                                        title = title_elem.text.strip() if title_elem else ""
                                        
                                        # 跳过太短或太长的标题
                                        if not title or len(title) < 10 or len(title) > 150:
                                            continue
                                        
                                        # 尝试提取链接
                                        url = ""
                                        link_elem = element.ele('tag:a')
                                        if link_elem:
                                            url = link_elem.attr('href') or ""
                                            
                                        # 规范化URL
                                        if url and not url.startswith('http'):
                                            url = urllib.parse.urljoin(source_url, url)
                                        
                                        # 尝试提取时间
                                        time_str = NewsParser.extract_date(element)
                                        
                                        # 添加到结果列表
                                        if not any(n['title'] == title for n in news_items):  # 避免重复
                                            news_items.append({
                                                'title': title,
                                                'date': time_str,
                                                'source': source_name,
                                                'url': url,
                                                'content': ""
                                            })
                                            print(f"找到新闻: {title[:30]}...")
                                        
                                        # 限制数量
                                        if len(news_items) >= 30:
                                            break
                                    except Exception as e:
                                        print(f"处理新闻项时出错: {e}")
                                        continue
                                
                                # 如果找到足够的新闻，就不再继续尝试其他选择器
                                if len(news_items) >= 10:
                                    break
                        except Exception as e:
                            print(f"使用选择器 {selector} 时出错: {e}")
                            continue
                    
                    # 如果上述方法没有找到新闻，尝试直接获取所有链接
                    if not news_items:
                        print("尝试获取所有可能的新闻链接")
                        try:
                            links = page.eles('tag:a')
                            for link in links:
                                try:
                                    text = link.text.strip()
                                    href = link.attr('href') or ""
                                    
                                    # 过滤掉导航、菜单等链接
                                    if (text and 15 <= len(text) <= 120 and 
                                        not any(word in text for word in ['登录', '注册', '关于', '首页', '联系'])):
                                        
                                        # 规范化URL
                                        if href and not href.startswith('http'):
                                            href = urllib.parse.urljoin(source_url, href)
                                        
                                        # 添加到结果列表
                                        if href and not any(n['title'] == text for n in news_items):
                                            # 尝试从整个链接元素和周围文本中提取日期
                                            date_str = NewsParser.extract_date(link)
                                            
                                            news_items.append({
                                                'title': text,
                                                'date': date_str,
                                                'source': source_name,
                                                'url': href,
                                                'content': ""
                                            })
                                            print(f"从链接找到新闻: {text[:30]}... 日期: {date_str}")
                                        
                                        # 限制数量
                                        if len(news_items) >= 30:
                                            break
                                except Exception as e:
                                    continue
                        except Exception as e:
                            print(f"获取链接时出错: {e}")
                except Exception as e:
                    print(f"DrissionPage解析元素出错: {e}")
                
                if news_items:
                    print(f"使用DrissionPage解析完成，共找到 {len(news_items)} 条新闻")
                    return news_items
                
                print("DrissionPage未找到新闻，尝试使用BeautifulSoup解析")
            
            # 如果DrissionPage不可用或没有找到新闻，使用BeautifulSoup
            return NewsParser.parse_generic(html_content, source_name, source_url)
            
        except Exception as e:
            print(f"使用DrissionPage解析页面时出错: {e}")
            import traceback
            print(traceback.format_exc())
            
            # 如果DrissionPage解析失败，尝试使用BeautifulSoup作为备选方案
            return NewsParser.parse_generic(html_content, source_name, source_url)
    
    @staticmethod
    def extract_url_from_onclick(onclick_attr, base_url):
        """从onclick属性中提取URL"""
        if not onclick_attr:
            return ""
        
        # 尝试提取单引号或双引号中的URL
        url_match = re.search(r"(?:window\.open|location\.href)\s*=\s*['\"]([^'\"]+)['\"]", onclick_attr)
        if url_match:
            url = url_match.group(1)
            if not url.startswith('http'):
                return urllib.parse.urljoin(base_url, url)
            return url
        return ""
    
    @staticmethod
    def parse_generic(html_content, source_name, source_url):
        """通用解析方法"""
        print("使用通用解析方法")
        soup = BeautifulSoup(html_content, 'html.parser')
        news_items = []
        
        # 为常见的财经网站添加特定的选择器
        finance_selectors = [
            # 通用财经新闻列表
            '.news-list li', '.news-list-content li', '.news-list .news-item', '.news-item', 
            '.content-list li', '.list-content li', '.news-wrap .news-item', '.newslist li',
            '.list li', '.news_item', '.news-box .item', '.news-list .item',
            
            # 东方财富
            '.newslist .news-item', '.article-list li', '.module-list .item', 
            '.newsList li', '.news_list li', '.news-container .news',
            
            # 新浪财经
            '.feed-item', '.feed-card-item', '.news-item-container', 
            '.news-list-container .slide-item', '.linkNewsTopyk a',
            
            # 凤凰财经
            '.news-stream-newsStream-news-item', '.news_item', '.news-list .item',
            '.news-stream-basic-news-item', '.news-stream-newsStream-news-item',
            
            # 金融界
            '.m-list li', '.newlist li', '.news ul li', '.hot-news li',
            
            # 同花顺
            '.content li', '.news-list div', '.list-con .item', '.m-list li',
            
            # 华尔街见闻
            '.article-entry', '.article-item', '.news-item'
        ]
        
        # 遍历所有选择器尝试查找新闻项
        print("尝试使用多个常见新闻选择器")
        for selector in finance_selectors:
            items = soup.select(selector)
            if items:
                print(f"使用选择器 '{selector}' 找到 {len(items)} 个项目")
                for item in items:
                    try:
                        # 尝试提取标题
                        title_elem = item.select_one('a, h2, h3, h4, .title, .news-title, .article-title, .tit, .h1, .h2, .content p')
                        if title_elem:
                            title = title_elem.get_text(strip=True)
                            
                            # 标题太短或太长的可能不是新闻标题
                            if not title or len(title) < 10 or len(title) > 150:
                                continue
                            
                            # 尝试提取链接
                            url = ""
                            if title_elem.name == 'a':
                                url = title_elem.get('href', '')
                            else:
                                link_elem = item.select_one('a')
                                if link_elem:
                                    url = link_elem.get('href', '')
                            
                            # 规范化URL
                            if url and not url.startswith('http'):
                                url = urllib.parse.urljoin(source_url, url)
                            
                            # 尝试提取时间
                            date_str = NewsParser.extract_date(item, str(item))
                            
                            # 临时存储，以标题为key，避免重复添加
                            if title:
                                # 检查是否已经添加过这个标题
                                if not any(n['title'] == title for n in news_items):
                                    news_items.append({
                                        'title': title,
                                        'date': date_str,
                                        'source': source_name,
                                        'url': url,
                                        'content': ""
                                    })
                                    print(f"找到新闻: {title[:30]}... 日期: {date_str}")
                        
                        # 限制最大数量
                        if len(news_items) >= 50:
                            print(f"已达到最大数量限制 (50)，停止解析")
                            break
                    except Exception as e:
                        print(f"处理新闻项时出错: {e}")
                        continue
            
            # 如果已经找到足够的新闻项，就不再继续尝试其他选择器
            if len(news_items) >= 20:
                print(f"已找到 {len(news_items)} 条新闻，不再尝试其他选择器")
                break
        
        # 如果上面的方法没有找到新闻，尝试更通用的方法
        if not news_items:
            print("尝试更通用的方法查找新闻")
            
            # 查找所有链接和标题
            all_links = soup.select('a')
            for link in all_links:
                try:
                    # 过滤掉导航栏、底部链接等
                    parent_classes = ' '.join(parent.get('class', []) for parent in link.parents if parent.name)
                    if any(skip in parent_classes.lower() for skip in ['header', 'footer', 'nav', 'menu', 'copyright']):
                        continue
                        
                    text = link.get_text(strip=True)
                    url = link.get('href', '')
                    
                    # 过滤掉非新闻链接
                    if not text or len(text) < 15 or len(text) > 120:
                        continue
                        
                    if url and not url.startswith('http'):
                        url = urllib.parse.urljoin(source_url, url)
                        
                    # 增加进入列表前的过滤
                    skip_words = ['登录', '注册', '关于', '联系', '帮助', '广告', '招聘', '合作']
                    if any(word in text for word in skip_words):
                        continue
                    
                    # 尝试提取日期
                    date_str = NewsParser.extract_date(link, str(link.parent))
                        
                    # 检查是否已经添加过这个标题
                    if text and not any(n['title'] == text for n in news_items):
                        news_items.append({
                            'title': text,
                            'date': date_str,
                            'source': source_name,
                            'url': url,
                            'content': ""
                        })
                        print(f"从链接找到新闻: {text[:30]}... 日期: {date_str}")
                        
                    # 限制数量
                    if len(news_items) >= 50:
                        break
                except Exception as e:
                    continue
                    
        # 如果仍然没有找到新闻，尝试最后的方法
        if not news_items:
            print("所有方法均未找到新闻，尝试直接提取页面中可能的新闻文本")
            
            # 尝试提取页面中的所有段落文本，可能包含新闻
            paragraphs = soup.select('p')
            for p in paragraphs:
                try:
                    text = p.get_text(strip=True)
                    # 只选取长度适中的段落文本作为可能的新闻
                    if text and 30 <= len(text) <= 200:
                        # 生成一个唯一的ID作为这条新闻的标题
                        
                        # 尝试从段落或其父元素中提取日期
                        date_str = NewsParser.extract_date(p, str(p.parent))
                        
                        news_items.append({
                            'title': text[:100] + ('...' if len(text) > 100 else ''),
                            'date': date_str,
                            'source': source_name,
                            'url': source_url,  # 使用原始URL
                            'content': text
                        })
                        print(f"从段落找到可能的新闻: {text[:30]}... 日期: {date_str}")
                        
                    # 限制数量
                    if len(news_items) >= 20:
                        break
                except Exception as e:
                    continue
        
        print(f"通用解析完成，找到 {len(news_items)} 条新闻")
        return news_items
    
    @staticmethod
    def parse_fenghuang(html_content, source_name, source_url):
        """解析凤凰网财经新闻"""
        print("使用凤凰网专用解析器")
        soup = BeautifulSoup(html_content, 'html.parser')
        news_items = []
        
        # 尝试匹配提供的特定结构
        key_news_items = soup.select('.key_news.news_item, .news_item, .feed-item, .news-item')
        if key_news_items:
            print(f"找到 {len(key_news_items)} 个凤凰网新闻项")
            for item in key_news_items:
                # 尝试获取时间
                time_elem = item.select_one('.news_datetime, .time')
                time_str = time_elem.get_text(strip=True) if time_elem else "未知时间"
                
                # 尝试获取内容
                content_elem = item.select_one('.news_content, .content, h2, h3, p')
                if content_elem:
                    content = content_elem.get_text(strip=True)
                    
                    # 构建新闻项
                    news_items.append({
                        'title': content,  # 内容也作为标题
                        'date': time_str,
                        'source': source_name,
                        'url': "",  # 可能没有链接
                        'content': content
                    })
                    print(f"找到凤凰网新闻: {content[:30]}... 日期: {time_str}")
        
        # 尝试更通用的结构
        if not news_items:
            # 尝试列表结构
            print("尝试凤凰网列表结构")
            list_items = soup.select('ul.list li, div.news-list div, div.content ul li')
            if list_items:
                print(f"找到 {len(list_items)} 个列表项")
                for item in list_items:
                    title_elem = item.select_one('a, h3, h4, p')
                    if title_elem:
                        title = title_elem.get_text(strip=True)
                        if title and len(title) > 5:
                            time_str = "未知时间"
                            time_elem = item.select_one('.time, .date, span')
                            if time_elem:
                                time_str = time_elem.get_text(strip=True)
                            
                            url = ""
                            if title_elem.name == 'a':
                                url = title_elem.get('href', '')
                            else:
                                link = item.select_one('a')
                                if link:
                                    url = link.get('href', '')
                            
                            if url and not url.startswith('http'):
                                url = urllib.parse.urljoin(source_url, url)
                            
                            news_items.append({
                                'title': title,
                                'date': time_str,
                                'source': source_name,
                                'url': url,
                                'content': ""
                            })
                            print(f"找到凤凰网列表新闻: {title[:30]}... 日期: {time_str}")
        
        # 如果没有找到特定结构，尝试通用解析
        if not news_items:
            print("凤凰网专用解析器未找到新闻，尝试通用解析方法")
            return NewsParser.parse_generic(html_content, source_name, source_url)
        
        print(f"凤凰网解析完成，找到 {len(news_items)} 条新闻")
        return news_items
    
    @staticmethod
    def parse_jinrongjie(html_content, source_name, source_url):
        """解析金融界新闻"""
        return NewsParser.parse_generic(html_content, source_name, source_url)
    
    @staticmethod
    def parse_10jqka(html_content, source_name, source_url):
        """解析同花顺新闻"""
        return NewsParser.parse_generic(html_content, source_name, source_url)
    
    @staticmethod
    def parse_sina(html_content, source_name, source_url):
        """解析新浪财经焦点新闻"""
        return NewsParser.parse_generic(html_content, source_name, source_url)
    
    @staticmethod
    def parse_yuncaijing(html_content, source_name, source_url):
        """解析云财经新闻"""
        return NewsParser.parse_generic(html_content, source_name, source_url)
    
    @staticmethod
    def parse_eastmoney(html_content, source_name, source_url):
        """解析东方财富新闻"""
        return NewsParser.parse_generic(html_content, source_name, source_url)
    
    @staticmethod
    def parse_wallstreetcn(html_content, source_name, source_url):
        """解析华尔街见闻新闻"""
        return NewsParser.parse_generic(html_content, source_name, source_url)
    
    @staticmethod
    def extract_content(url):
        """提取新闻正文内容，使用DrissionPage或备选方法"""
        if not url:
            return ""
        
        try:
            print(f"开始提取内容: {url}")
            
            # 获取页面
            page = NewsParser.get_page()
            
            # 如果DrissionPage不可用，使用传统方法
            if page is None:
                print("DrissionPage不可用，使用传统方法获取内容")
                html_content = NewsParser.fetch_page(url)
                if not html_content:
                    return "无法获取页面内容"
                
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # 尝试多种可能的正文容器选择器
                content_selectors = [
                    '.article-content', '.content', '.news-content', 'article',
                    '.detail-content', '.article-body', '.main-content',
                    '#ContentBody', '#artibody', '.article_content', '.art_content',
                    '.article', '.post-content', '.entry-content', '.story-body'
                ]
                
                for selector in content_selectors:
                    content_elem = soup.select_one(selector)
                    if content_elem:
                        print(f"找到内容容器: {selector}")
                        # 删除不需要的元素
                        for unwanted in content_elem.select('script, style, .relate-news, .article-share, .copyright, .share, .social, .recommend'):
                            unwanted.decompose()
                        
                        # 格式化文本，保留段落结构
                        paragraphs = content_elem.select('p')
                        if paragraphs:
                            content = '\n\n'.join(p.get_text(strip=True) for p in paragraphs if p.get_text(strip=True))
                            print(f"成功提取内容，长度: {len(content)}")
                            return content
                        else:
                            content = content_elem.get_text('\n', strip=True)
                            print(f"成功提取内容，长度: {len(content)}")
                            return content
                
                # 如果上面的方法都失败了，尝试提取所有段落
                paragraphs = soup.select('p')
                if paragraphs:
                    text = '\n\n'.join(p.get_text(strip=True) for p in paragraphs 
                                      if len(p.get_text(strip=True)) > 20  # 过滤太短的段落
                                      and not p.select('script, style'))  # 过滤包含脚本的段落
                    if text:
                        print(f"通过段落提取内容，长度: {len(text)}")
                        return text
                
                print("无法找到合适的内容容器")
                return "无法提取正文内容"
            
            # 使用DrissionPage获取内容
            try:
                page.get(url)
                
                # 等待页面加载
                time.sleep(2)
                
                # 保存截图
                try:
                    screenshot_path = f"content_screenshot_{url.split('/')[-1].split('?')[0]}.png"
                    page.save_screenshot(screenshot_path)
                    print(f"已保存内容页面截图到 {screenshot_path}")
                except Exception as e:
                    print(f"保存内容截图失败: {e}")
                
                # 尝试各种可能的正文容器选择器
                content_selectors = [
                    '.article-content', '.content', '.news-content', 'tag:article',
                    '.detail-content', '.article-body', '.main-content',
                    '#ContentBody', '#artibody', '.article_content', '.art_content',
                    '.article', '.post-content', '.entry-content', '.story-body'
                ]
                
                for selector in content_selectors:
                    content_elem = page.ele(selector)
                    if content_elem:
                        print(f"找到内容容器: {selector}")
                        
                        # 尝试提取段落
                        paragraphs = content_elem.eles('tag:p')
                        if paragraphs:
                            content = '\n\n'.join(p.text.strip() for p in paragraphs if p.text.strip())
                            if content:
                                print(f"成功提取内容，长度: {len(content)}")
                                return content
                        
                        # 如果没有找到段落，直接使用容器文本
                        content = content_elem.text.strip()
                        if content:
                            print(f"成功提取内容，长度: {len(content)}")
                            return content
                
                # 如果上述方法都失败，尝试提取所有段落
                paragraphs = page.eles('tag:p')
                if paragraphs:
                    content = '\n\n'.join(p.text.strip() for p in paragraphs 
                                         if p.text.strip() and len(p.text.strip()) > 20)
                    if content:
                        print(f"通过段落提取内容，长度: {len(content)}")
                        return content
                
                print("无法找到合适的内容容器")
                return "无法提取正文内容"
            except Exception as e:
                print(f"使用DrissionPage提取内容失败: {e}，尝试使用传统方法")
                # 回退到传统方法
                return NewsParser.extract_content_fallback(url)
            
        except Exception as e:
            print(f"提取内容出错: {e}")
            import traceback
            print(traceback.format_exc())
            return "获取内容时出错"
    
    @staticmethod
    def extract_content_fallback(url):
        """传统方法提取内容（备选方案）"""
        try:
            html_content = NewsParser.fetch_page(url)
            if not html_content:
                return "无法获取页面内容"
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 尝试多种可能的正文容器选择器
            content_selectors = [
                '.article-content', '.content', '.news-content', 'article',
                '.detail-content', '.article-body', '.main-content',
                '#ContentBody', '#artibody', '.article_content', '.art_content',
                '.article', '.post-content', '.entry-content', '.story-body'
            ]
            
            for selector in content_selectors:
                content_elem = soup.select_one(selector)
                if content_elem:
                    print(f"找到内容容器: {selector}")
                    # 删除不需要的元素
                    for unwanted in content_elem.select('script, style, .relate-news, .article-share, .copyright, .share, .social, .recommend'):
                        unwanted.decompose()
                    
                    # 格式化文本，保留段落结构
                    paragraphs = content_elem.select('p')
                    if paragraphs:
                        content = '\n\n'.join(p.get_text(strip=True) for p in paragraphs if p.get_text(strip=True))
                        print(f"成功提取内容，长度: {len(content)}")
                        return content
                    else:
                        content = content_elem.get_text('\n', strip=True)
                        print(f"成功提取内容，长度: {len(content)}")
                        return content
            
            # 如果上面的方法都失败了，尝试提取所有段落
            paragraphs = soup.select('p')
            if paragraphs:
                text = '\n\n'.join(p.get_text(strip=True) for p in paragraphs 
                                  if len(p.get_text(strip=True)) > 20  # 过滤太短的段落
                                  and not p.select('script, style'))  # 过滤包含脚本的段落
                if text:
                    print(f"通过段落提取内容，长度: {len(text)}")
                    return text
            
            print("无法找到合适的内容容器")
            return "无法提取正文内容"
            
        except Exception as e:
            print(f"备选方法提取内容出错: {e}")
            return "获取内容时出错"
    
    @staticmethod
    def extract_date(element, item_html=None, default_date="未知时间"):
        """提取新闻日期，尝试多种选择器和格式化方法"""
        try:
            # 尝试多种可能的日期选择器
            date_selectors = [
                '.time', '.date', '.news-date', '.news-time', '.publish-time', '.article-time', 
                '.meta', '.timer', '.pubtime', '.news_time', '.date-source', '.article-date',
                '.author-time', '.post-time', '.entry-date', '.timestamp', '.news-detail-time',
                '[data-time]', '[datetime]', '.news-left', '.date-and-source', '.article-info'
            ]
            
            # 如果提供的是DrissionPage元素
            if element and hasattr(element, 'ele'):
                for selector in date_selectors:
                    try:
                        time_elem = element.ele(selector)
                        if time_elem:
                            date_text = time_elem.text.strip()
                            if date_text and len(date_text) > 2:
                                print(f"找到日期元素: {selector}, 内容: {date_text}")
                                return NewsParser.format_date(date_text)
                    except Exception as e:
                        print(f"DrissionPage提取日期 {selector} 出错: {e}")
                        continue
                
                # 检查其他可能包含日期的元素
                try:
                    all_text = element.text.strip()
                    date = NewsParser.extract_date_from_text(all_text)
                    if date:
                        return date
                except Exception as e:
                    print(f"提取DrissionPage元素文本中的日期出错: {e}")
            
            # 如果提供的是BeautifulSoup元素
            elif element and hasattr(element, 'select_one'):
                for selector in date_selectors:
                    try:
                        time_elem = element.select_one(selector)
                        if time_elem:
                            date_text = time_elem.get_text(strip=True)
                            if date_text and len(date_text) > 2:
                                print(f"找到日期元素: {selector}, 内容: {date_text}")
                                return NewsParser.format_date(date_text)
                    except Exception as e:
                        print(f"BeautifulSoup提取日期 {selector} 出错: {e}")
                        continue
                
                # 检查可能含有日期的属性
                try:
                    for attr in ['data-time', 'datetime', 'data-date', 'pubdate']:
                        for tag in element.select(f'[{attr}]'):
                            try:
                                date_attr = tag.get(attr)
                                if date_attr:
                                    return NewsParser.format_date(date_attr)
                            except Exception:
                                continue
                except Exception as e:
                    print(f"提取元素属性中的日期出错: {e}")
                
                # 尝试从元素的所有文本中提取日期
                try:
                    all_text = element.get_text(strip=True)
                    date = NewsParser.extract_date_from_text(all_text)
                    if date:
                        return date
                except Exception as e:
                    print(f"提取BeautifulSoup元素文本中的日期出错: {e}")
            
            # 如果提供了HTML字符串，尝试直接从中提取日期
            if item_html:
                try:
                    date = NewsParser.extract_date_from_text(item_html)
                    if date:
                        return date
                except Exception as e:
                    print(f"从HTML字符串中提取日期出错: {e}")
            
            return default_date
            
        except Exception as e:
            print(f"提取日期时出错: {e}")
            import traceback
            print(f"日期提取错误详情: {traceback.format_exc()}")
            return default_date
    
    @staticmethod
    def extract_date_from_text(text):
        """从文本中提取日期"""
        if not text:
            return None
            
        # 常见的日期格式正则表达式
        date_patterns = [
            # 标准格式：2023-04-05 12:34:56 或 2023/04/05 12:34:56
            r'(\d{4}[-/\.]\d{1,2}[-/\.]\d{1,2}[\s]+\d{1,2}:\d{1,2}(:\d{1,2})?)',
            # 短日期时间：2023-04-05 或 2023/04/05
            r'(\d{4}[-/\.]\d{1,2}[-/\.]\d{1,2})',
            # 常见中文格式：2023年04月05日 12:34 或不带时间
            r'(\d{4}年\d{1,2}月\d{1,2}日[\s]*(\d{1,2}:\d{1,2}(:\d{1,2})?)?)',
            # 简化中文格式：2023年4月5日
            r'(\d{4}年\d{1,2}月\d{1,2}日)',
            # 月日年格式：04-05-2023 或 04/05/2023
            r'(\d{1,2}[-/\.]\d{1,2}[-/\.]\d{4})',
            # 时间在前：12:34 2023-04-05
            r'(\d{1,2}:\d{1,2}(:\d{1,2})?[\s]+\d{4}[-/\.]\d{1,2}[-/\.]\d{1,2})',
            # 相对时间格式（如"1小时前"、"昨天"等）
            r'(\d+分钟前|\d+小时前|昨天|\d+天前)'
        ]
        
        for pattern in date_patterns:
            try:
                matches = re.search(pattern, text)
                if matches:
                    date_text = matches.group(1)
                    return NewsParser.format_date(date_text)
            except Exception as e:
                print(f"提取日期模式 {pattern} 时出错: {e}")
                continue
                
        return None
    
    @staticmethod
    def format_date(date_str):
        """格式化日期字符串"""
        if not date_str:
            return "未知时间"
            
        try:
            date_str = date_str.strip()
            print(f"尝试格式化日期: {date_str}")
            
            # 如果是相对时间，转换为绝对时间
            if re.match(r'\d+分钟前', date_str):
                try:
                    minutes_match = re.search(r'(\d+)分钟前', date_str)
                    if minutes_match:
                        minutes = int(minutes_match.group(1))
                        date = datetime.now() - timedelta(minutes=minutes)
                        return date.strftime('%Y-%m-%d %H:%M')
                except Exception as e:
                    print(f"处理'分钟前'格式错误: {e}")
                
            if re.match(r'\d+小时前', date_str):
                try:
                    hours_match = re.search(r'(\d+)小时前', date_str)
                    if hours_match:
                        hours = int(hours_match.group(1))
                        date = datetime.now() - timedelta(hours=hours)
                        return date.strftime('%Y-%m-%d %H:%M')
                except Exception as e:
                    print(f"处理'小时前'格式错误: {e}")
                
            if '昨天' in date_str:
                try:
                    date = datetime.now() - timedelta(days=1)
                    time_part = re.search(r'(\d{1,2}:\d{1,2}(:\d{1,2})?)', date_str)
                    if time_part:
                        return date.strftime('%Y-%m-%d') + ' ' + time_part.group(1)
                    return date.strftime('%Y-%m-%d')
                except Exception as e:
                    print(f"处理'昨天'格式错误: {e}")
                
            if re.match(r'\d+天前', date_str):
                try:
                    days_match = re.search(r'(\d+)天前', date_str)
                    if days_match:
                        days = int(days_match.group(1))
                        date = datetime.now() - timedelta(days=days)
                        return date.strftime('%Y-%m-%d')
                except Exception as e:
                    print(f"处理'天前'格式错误: {e}")
            
            # 处理中文日期格式
            if '年' in date_str and '月' in date_str and '日' in date_str:
                try:
                    year_match = re.search(r'(\d{4})年', date_str)
                    month_match = re.search(r'年(\d{1,2})月', date_str)
                    day_match = re.search(r'月(\d{1,2})日', date_str)
                    
                    if year_match and month_match and day_match:
                        year = int(year_match.group(1))
                        month = int(month_match.group(1))
                        day = int(day_match.group(1))
                        
                        time_part = re.search(r'(\d{1,2}:\d{1,2}(:\d{1,2})?)', date_str)
                        if time_part:
                            return f"{year}-{month:02d}-{day:02d} {time_part.group(1)}"
                        return f"{year}-{month:02d}-{day:02d}"
                except Exception as e:
                    print(f"处理中文日期格式错误: {e}")
                
            # 如果日期字符串太长，可能包含其他文本，尝试提取核心部分
            if len(date_str) > 25:
                try:
                    date_patterns = [
                        r'(\d{4}[-/\.]\d{1,2}[-/\.]\d{1,2}[\s]+\d{1,2}:\d{1,2}(:\d{1,2})?)',
                        r'(\d{4}[-/\.]\d{1,2}[-/\.]\d{1,2})',
                        r'(\d{1,2}:\d{1,2}(:\d{1,2})?[\s]+\d{4}[-/\.]\d{1,2}[-/\.]\d{1,2})'
                    ]
                    for pattern in date_patterns:
                        matches = re.search(pattern, date_str)
                        if matches:
                            date_str = matches.group(1)
                            break
                except Exception as e:
                    print(f"提取核心日期部分错误: {e}")
            
            # 移除可能的无关前缀/后缀(如"发布时间："、"来源：xxx")
            try:
                date_str = re.sub(r'^(.*?)[发布时间|时间|日期|发表于|发布于|上传于|来源|作者]+[：:：\s]+', '', date_str)
                date_str = date_str.strip()
            except Exception as e:
                print(f"清理日期字符串错误: {e}")
            
            # 返回清理后的日期字符串
            if re.match(r'\d{4}[-/\.]\d{1,2}[-/\.]\d{1,2}', date_str) or \
               re.match(r'\d{1,2}[-/\.]\d{1,2}[-/\.]\d{4}', date_str) or \
               re.search(r'\d{1,2}:\d{1,2}', date_str):
                return date_str
                
            # 如果无法解析，返回原始字符串
            return date_str if date_str else "未知时间"
            
        except Exception as e:
            print(f"格式化日期时出错: {e}")
            return "未知时间" 