import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)



# 添加项目根目录到Python路径

from config import get_token
import tushare as ts
import pandas as pd
import sqlite3
import time
from datetime import datetime, timedelta
# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

class StockDatabase:
    def __init__(self, db_name='data.db'):
        """初始化数据库连接"""
        self.db_name = db_name
        self.conn = sqlite3.connect(db_name)
        print(f"数据库 {db_name} 连接成功")

    def create_table(self):
        """创建周线行情表"""
        create_table_sql = '''CREATE TABLE IF NOT EXISTS weekly (
            ts_code TEXT,
            trade_date TEXT,
            close REAL,
            open REAL,
            high REAL,
            low REAL,
            pre_close REAL,
            change REAL,
            pct_chg REAL,
            vol REAL,
            amount REAL,
            PRIMARY KEY (ts_code, trade_date)
        )'''
        
        try:
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 weekly 创建成功")
        except Exception as e:
            print(f"创建表格 weekly 时出错: {e}")

    def check_data_exists(self, ts_code, start_date, end_date):
        """检查指定股票和时间范围的数据是否已存在"""
        try:
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT COUNT(*) FROM weekly
                WHERE ts_code = ? AND trade_date >= ? AND trade_date <= ?
            ''', (ts_code, start_date, end_date))
            count = cursor.fetchone()[0]
            return count > 0
        except Exception as e:
            print(f"检查数据是否存在时出错: {e}")
            return False

    def insert_data(self, df, ts_code=None):
        """将DataFrame数据插入到表格中"""
        try:
            if df is None or df.empty:
                print("没有数据需要写入")
                return

            # 如果提供了ts_code，检查数据是否已存在
            if ts_code:
                # 获取数据的时间范围
                if 'trade_date' in df.columns:
                    min_date = df['trade_date'].min()
                    max_date = df['trade_date'].max()

                    if self.check_data_exists(ts_code, min_date, max_date):
                        print(f"数据已存在: {ts_code} ({min_date} 到 {max_date})，跳过更新")
                        return

            # 尝试插入数据
            df.to_sql('weekly', self.conn, if_exists='append', index=False)
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")

        except Exception as e:
            error_msg = str(e)
            if "UNIQUE constraint failed" in error_msg:
                print(f"数据已存在，跳过更新: {error_msg}")
            else:
                print(f"写入数据时出错: {e}")
                self.conn.rollback()

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

class APIRateLimiter:
    def __init__(self, max_calls_per_minute=2):
        self.max_calls = max_calls_per_minute
        self.calls = []
        
    def wait_if_needed(self):
        """检查并等待，确保不超过频率限制"""
        now = datetime.now()
        # 清理超过1分钟的调用记录
        self.calls = [call_time for call_time in self.calls 
                     if (now - call_time).total_seconds() < 60]
        
        if len(self.calls) >= self.max_calls:
            # 计算需要等待的时间
            wait_time = 60 - (now - self.calls[0]).total_seconds()
            if wait_time > 0:
                print(f"达到频率限制，等待 {wait_time:.1f} 秒...")
                time.sleep(wait_time)
            # 清理已过期的调用记录
            self.calls = self.calls[1:]
        
        # 记录新的调用时间
        self.calls.append(now)

def get_stock_list():
    """获取股票列表"""
    try:
        df = pro.stock_basic(exchange='', list_status='L', 
                            fields='ts_code,symbol,name,area,industry,list_date')
        return df
    except Exception as e:
        print(f"获取股票列表时出错: {e}")
        return None

def fetch_weekly_data(ts_code, start_date, end_date, rate_limiter):
    """获取单个股票的周线数据"""
    try:
        # 等待以确保不超过频率限制
        rate_limiter.wait_if_needed()
        
        df = pro.weekly(ts_code=ts_code,
                       start_date=start_date,
                       end_date=end_date,
                       fields='ts_code,trade_date,open,high,low,close,pre_close,change,pct_chg,vol,amount')
        return df
    except Exception as e:
        print(f"获取 {ts_code} 的周线数据时出错: {e}")
        return None

def main():
    # 确保market_data目录存在
    os.makedirs('market_data', exist_ok=True)
    
    # 创建数据库实例和频率限制器
    db = StockDatabase()
    db.create_table()
    rate_limiter = APIRateLimiter(max_calls_per_minute=2)
    
    try:
        # 获取股票列表
        stock_list = get_stock_list()
        if stock_list is None:
            return
        
        # 设置时间范围（近3年）
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=3*365)).strftime('%Y%m%d')
        
        total_stocks = len(stock_list)
        for idx, row in stock_list.iterrows():
            ts_code = row['ts_code']
            print(f"正在处理第 {idx+1}/{total_stocks} 只股票: {ts_code}")
            
            try:
                # 获取周线数据
                df = fetch_weekly_data(ts_code, start_date, end_date, rate_limiter)
                if df is not None and not df.empty:
                    db.insert_data(df)
                    print(f"成功获取 {ts_code} 的周线数据，共 {len(df)} 条记录")
            except Exception as e:
                print(f"处理 {ts_code} 时出错: {e}")
                continue
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行A股周线行情数据获取程序...")
    main()
    print("程序运行完成！") 