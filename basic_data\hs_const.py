import os
import sys
from pathlib import Path
import time

# 获取项目根目录
root_dir = str(Path(__file__).resolve().parent.parent)
sys.path.insert(0, root_dir)

from config import get_token
import tushare as ts
import pandas as pd
from datetime import datetime, timedelta
import sqlite3

# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

class HSConstDatabase:
    def __init__(self, db_name='data.db'):
        """初始化数据库连接"""
        try:
            # 使用项目根目录的数据库路径
            self.db_path = os.path.join(root_dir, db_name)
            self.conn = sqlite3.connect(self.db_path)
            print(f"数据库 {self.db_path} 连接成功")
        except sqlite3.Error as e:
            print(f"数据库连接错误: {e}")
            raise

    def create_table(self):
        """创建沪深股通成分表"""
        try:
            create_table_sql = '''CREATE TABLE IF NOT EXISTS hs_const (
                ts_code TEXT,
                hs_type TEXT,
                in_date TEXT,
                out_date TEXT,
                is_new TEXT,
                PRIMARY KEY (ts_code, hs_type, in_date)
            )'''
            
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 hs_const 创建成功")
            
        except Exception as e:
            print(f"创建表格 hs_const 时出错: {e}")
            raise

    def get_existing_records(self):
        """获取数据库中已存在的记录"""
        try:
            cursor = self.conn.execute('''
                SELECT ts_code, hs_type, in_date 
                FROM hs_const
            ''')
            return {(row[0], row[1], row[2]) for row in cursor.fetchall()}
        except Exception as e:
            print(f"获取已存在记录时出错: {e}")
            return set()

    def insert_data(self, df):
        """将DataFrame数据插入到表格中"""
        try:
            if df is None or df.empty:
                return
                
            # 创建临时表
            temp_table_name = 'temp_hs_const'
            df.to_sql(temp_table_name, self.conn, if_exists='replace', index=False)
            
            # 使用INSERT OR REPLACE将数据从临时表插入到主表
            insert_sql = f'''
                INSERT OR REPLACE INTO hs_const 
                SELECT * FROM {temp_table_name}
            '''
            self.conn.execute(insert_sql)
            
            # 删除临时表
            self.conn.execute(f'DROP TABLE IF EXISTS {temp_table_name}')
            
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")
        except Exception as e:
            print(f"写入数据时出错: {e}")
            self.conn.rollback()

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def fetch_hs_const_data(hs_type, start_date, end_date, existing_records):
    """获取沪深股通成分数据"""
    try:
        print(f"正在获取 {hs_type} 的沪深股通成分数据...")
        df = pro.hs_const(hs_type=hs_type, 
                         start_date=start_date,
                         end_date=end_date,
                         is_new='')

        if df is not None and not df.empty:
            # 过滤出新记录
            df['record_id'] = df.apply(lambda x: (x['ts_code'], x['hs_type'], x['in_date']), axis=1)
            new_records = df[~df['record_id'].isin([rec for rec in existing_records])]
            new_records = new_records.drop('record_id', axis=1)
            
            if not new_records.empty:
                print(f"获取到 {len(new_records)} 条新的沪深股通成分记录")
                return new_records
            else:
                print("没有新的沪深股通成分记录")
                return None
    except Exception as e:
        print(f"获取沪深股通成分数据时出错: {e}")
        return None

def main():
    # 确保数据目录存在
    os.makedirs('basic_data', exist_ok=True)
    
    # 创建数据库实例
    db = HSConstDatabase()
    
    try:
        # 创建表（如果不存在）
        db.create_table()
        
        # 获取已存在的记录
        existing_records = db.get_existing_records()
        
        # 计算日期范围（近5年）
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=3*365)).strftime('%Y%m%d')
        
        print(f"获取从 {start_date} 到 {end_date} 的沪深股通成分数据...")
        
        # 获取沪深股通成分数据
        all_data = []
        for hs_type in ['SH', 'SZ']:
            df = fetch_hs_const_data(hs_type, start_date, end_date, existing_records)
            if df is not None and not df.empty:
                all_data.append(df)
            time.sleep(0.3)  # 添加延时以避免频率限制
        
        if all_data:
            # 合并所有数据
            combined_df = pd.concat(all_data, ignore_index=True)
            # 保存新数据
            db.insert_data(combined_df)
            print(f"共获取到 {len(combined_df)} 条新的沪深股通成分记录")
        else:
            print("没有新的沪深股通成分记录需要更新")
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行沪深股通成分数据获取程序...")
    main()
    print("程序运行完成！") 