# 数据获取模块: daily
import sqlite3
import pandas as pd
import logging
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

# 导入现有的日线行情数据获取模块
from market_data.daily import fetch_daily_data, StockDatabase

logger = logging.getLogger(f"DataFetcher.{__name__}")

def get_tables():
    """返回此模块可以处理的表名列表"""
    return ["daily"]

def fetch_data(table_name, db_path):
    """获取日线行情数据并存入数据库
    
    Args:
        table_name: 表名
        db_path: 数据库路径
        
    Returns:
        dict: 包含处理结果信息的字典
    """
    if table_name != "daily":
        return {"success": False, "message": f"此模块不处理表 {table_name}"}
    
    try:
        logger.info("正在获取日线行情数据...")
        
        # 创建数据库实例
        db = StockDatabase(db_path)
        
        try:
            # 获取日线行情数据
            daily_df = fetch_daily_data()
            
            if daily_df is not None and not daily_df.empty:
                # 创建表并保存数据
                db.create_table(table_name, daily_df)
                db.insert_data(table_name, daily_df)
                logger.info(f"成功获取日线行情数据，共 {len(daily_df)} 条记录")
                return {"success": True, "message": f"成功获取日线行情数据", "count": len(daily_df)}
            else:
                logger.info("未获取到日线行情数据")
                return {"success": False, "message": "未获取到日线行情数据", "count": 0}
        finally:
            # 确保数据库连接被关闭
            db.close()
            
    except Exception as e:
        logger.error(f"获取日线行情数据时出错: {e}")
        return {"success": False, "message": str(e)} 