# 股票数据获取GUI管理面板使用说明

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）
```bash
python start_gui.py
```

### 方法二：Windows批处理文件
双击运行 `start_gui.bat`

### 方法三：直接运行
```bash
python gui/app.py
```

## 📋 功能概览

### 主要功能
- ✅ **脚本管理**: 自动发现项目中的所有数据获取脚本
- ✅ **并行执行**: 支持1-10个脚本同时运行
- ✅ **实时监控**: 显示每个脚本的运行状态和进度
- ✅ **日志记录**: 彩色日志显示，支持不同级别消息
- ✅ **数据库管理**: 检查数据库连接状态
- ✅ **配置保存**: 自动保存窗口位置和用户设置

### 支持的数据模块
| 模块 | 说明 | 脚本数量 |
|------|------|----------|
| basic_data | 基础数据（股票信息、交易日历等） | ~9个 |
| market_data | 市场数据（日线、周线、月线等） | ~16个 |
| financial_data | 财务数据（三大报表、财务指标等） | ~12个 |
| moneyflow_data | 资金流向数据 | ~8个 |
| reference_data | 参考数据（概念股、大宗交易等） | ~9个 |
| special_data | 特色数据（因子、筹码等） | ~10个 |
| dapan_topic_data | 大盘主题数据（指数、龙虎榜等） | ~15个 |

## 🎮 界面操作指南

### 1. 控制面板区域

#### 数据库状态
- **绿色"已连接"**: 数据库连接正常
- **红色"连接失败"**: 数据库连接有问题
- 点击"检查数据库连接"按钮重新检测

#### 并发控制
- **最大并发数**: 设置同时运行的脚本数量（1-10）
- **总体进度**: 显示当前执行的整体进度
- 建议设置：
  - 网络较慢：1-2个
  - 网络正常：3-4个
  - 网络较快：5-8个

#### 主要按钮
- **运行选中脚本**: 只运行勾选的脚本
- **运行全部脚本**: 运行所有可用脚本
- **停止所有任务**: 强制停止正在运行的任务
- **清除日志**: 清空日志显示区域

### 2. 脚本列表区域

#### 选择脚本
- 点击脚本前的 ☐ 变成 ☑ 表示选中
- 再次点击可取消选择
- 支持多选

#### 状态说明
- **就绪**: 脚本准备运行
- **等待中**: 脚本已提交，等待执行
- **运行中**: 脚本正在执行
- **完成**: 脚本执行成功
- **失败**: 脚本执行失败

#### 进度信息
- 显示脚本执行进度百分比
- 显示最后运行时间

### 3. 日志区域

#### 日志颜色
- **黑色**: 普通信息
- **绿色**: 成功消息
- **红色**: 错误消息  
- **橙色**: 警告消息

#### 日志内容
- 显示详细的执行过程
- 包含时间戳
- 支持滚动查看历史日志

## ⚙️ 配置说明

### 自动配置文件
GUI会在 `gui/gui_config.json` 中保存配置：

```json
{
  "window": {
    "width": 1400,
    "height": 900,
    "x": 100,
    "y": 100
  },
  "execution": {
    "max_workers": 4
  }
}
```

### 配置项说明
- **window**: 窗口大小和位置（自动保存）
- **execution.max_workers**: 默认并发数

## 🔧 使用技巧

### 1. 首次使用
1. 启动GUI后，先点击"检查数据库连接"
2. 点击"刷新脚本列表"加载所有脚本
3. 选择需要的脚本或直接"运行全部脚本"

### 2. 推荐运行顺序
建议按以下顺序运行脚本：
1. **基础数据**: 先运行 `trade_cal.py`（交易日历）
2. **股票信息**: 运行 `stock_data.py`（股票基本信息）
3. **其他模块**: 根据需要选择运行

### 3. 性能优化
- **网络较慢时**: 减少并发数到1-2个
- **API限制**: 注意Tushare API的调用频率限制
- **内存使用**: 大量数据获取时注意内存使用情况

### 4. 错误处理
- 查看日志区域的详细错误信息
- 检查网络连接和API token配置
- 必要时重启GUI重新尝试

## 🚨 常见问题

### Q1: GUI无法启动
**A**: 检查以下项目：
- Python环境是否正确
- tkinter是否已安装
- 项目依赖是否完整

### Q2: 脚本执行失败
**A**: 可能原因：
- Tushare token未配置或已过期
- 网络连接问题
- API调用频率超限
- 数据库权限问题
- 脚本中缺少`main()`函数

### Q3: 界面卡顿
**A**: 解决方法：
- 减少并发数
- 清除日志内容
- 关闭其他占用资源的程序

### Q4: 数据库连接失败
**A**: 检查项目：
- `data.db` 文件是否存在
- 文件权限是否正确
- 磁盘空间是否充足

### Q5: 脚本列表显示很少
**A**: 可能原因：
- 脚本文件中没有`main()`函数
- 脚本文件名以下划线开头
- 模块目录不存在
- 文件读取权限问题

### Q6: "ImportError"或"ModuleNotFoundError"
**A**: 解决方法：
- 确保所有依赖包已安装
- 检查Python路径配置
- 重启GUI重新尝试

## 📝 注意事项

### 1. API使用
- 确保Tushare Pro token已正确配置
- 注意API调用频率限制
- 避免同时运行过多脚本

### 2. 数据存储
- 所有数据保存在 `data.db` SQLite数据库中
- 定期备份数据库文件
- 注意磁盘空间使用

### 3. 系统资源
- 并行运行会占用更多CPU和内存
- 网络带宽可能成为瓶颈
- 建议在空闲时间运行大量数据获取

## 🔄 更新和维护

### 添加新脚本
1. 在相应模块目录中添加Python脚本
2. 确保脚本包含 `main()` 函数
3. 重启GUI或点击"刷新脚本列表"

### 修改配置
- 直接编辑 `gui/gui_config.json` 文件
- 或通过GUI界面修改（自动保存）

### 日志管理
- GUI日志显示在界面中
- 各脚本的详细日志可能保存在单独的日志文件中

---

**技术支持**: 如遇问题，请查看项目文档或提交Issue
