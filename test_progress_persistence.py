#!/usr/bin/env python3
"""
测试进度持久化功能
验证退出程序后进度和最后运行时间是否保留
"""

import os
import sys
import time
import sqlite3
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from gui.resume_manager import ResumeManager

def create_test_progress():
    """创建测试进度数据"""
    print("=== 创建测试进度数据 ===")
    
    manager = ResumeManager()
    
    # 创建多个脚本的进度记录
    test_scripts = [
        {
            'name': 'daily.py',
            'status': 'completed',
            'total': 100,
            'completed': 100,
            'failed': 0
        },
        {
            'name': 'daily_basic.py',
            'status': 'interrupted',
            'total': 200,
            'completed': 150,
            'failed': 5
        },
        {
            'name': 'stock_basic.py',
            'status': 'failed',
            'total': 50,
            'completed': 30,
            'failed': 20
        },
        {
            'name': 'income.py',
            'status': 'running',
            'total': 300,
            'completed': 80,
            'failed': 2
        }
    ]
    
    for script in test_scripts:
        print(f"创建 {script['name']} 的进度记录...")
        
        # 保存脚本进度
        manager.save_script_progress(
            script_name=script['name'],
            status=script['status'],
            total_items=script['total'],
            completed_items=script['completed'],
            failed_items=script['failed'],
            last_processed_item=f"00000{script['completed']}.SZ",
            notes=f"测试数据 - {script['status']}"
        )
        
        # 为部分脚本创建股票级别的进度
        if script['completed'] > 0:
            for i in range(min(script['completed'], 10)):  # 只创建前10个股票的记录
                stock_code = f"{i+1:06d}.SZ"
                manager.save_stock_progress(
                    script_name=script['name'],
                    ts_code=stock_code,
                    last_date=datetime.now().strftime('%Y%m%d'),
                    status='completed',
                    record_count=100 + i
                )
        
        time.sleep(0.1)  # 确保时间戳不同
    
    print("测试进度数据创建完成！")

def view_progress():
    """查看当前进度"""
    print("\n=== 当前进度总览 ===")
    
    manager = ResumeManager()
    all_progress = manager.get_all_progress()
    
    if not all_progress:
        print("没有找到任何进度记录")
        return
    
    print(f"{'脚本名称':<20} {'状态':<12} {'进度':<8} {'完成/总数':<12} {'失败':<6} {'最后运行时间':<20}")
    print("-" * 85)
    
    for progress in all_progress:
        last_run = progress['last_run_time'][:19] if progress['last_run_time'] else 'N/A'
        print(f"{progress['script_name']:<20} "
              f"{progress['status']:<12} "
              f"{progress['progress_percent']:>6.1f}% "
              f"{progress['completed_items']:>4}/{progress['total_items']:<4} "
              f"{progress['failed_items']:>4} "
              f"{last_run:<20}")

def test_persistence():
    """测试持久化功能"""
    print("\n=== 测试持久化功能 ===")
    
    # 检查数据库文件是否存在
    db_path = "gui/progress.db"
    if os.path.exists(db_path):
        print(f"✅ 进度数据库文件存在: {db_path}")
        
        # 检查文件大小
        file_size = os.path.getsize(db_path)
        print(f"📊 数据库文件大小: {file_size} 字节")
        
        # 检查表结构
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查脚本进度表
        cursor.execute("SELECT COUNT(*) FROM script_progress")
        script_count = cursor.fetchone()[0]
        print(f"📝 脚本进度记录数: {script_count}")
        
        # 检查股票进度表
        cursor.execute("SELECT COUNT(*) FROM stock_progress")
        stock_count = cursor.fetchone()[0]
        print(f"📈 股票进度记录数: {stock_count}")
        
        # 检查最新记录的时间
        cursor.execute("SELECT MAX(last_run_time) FROM script_progress")
        latest_time = cursor.fetchone()[0]
        print(f"⏰ 最新记录时间: {latest_time}")
        
        conn.close()
    else:
        print(f"❌ 进度数据库文件不存在: {db_path}")

def simulate_program_restart():
    """模拟程序重启"""
    print("\n=== 模拟程序重启 ===")
    
    print("1. 保存当前状态...")
    manager = ResumeManager()
    
    # 模拟一个正在运行的脚本被中断
    manager.save_script_progress(
        script_name="test_restart.py",
        status="interrupted",
        total_items=500,
        completed_items=250,
        failed_items=10,
        last_processed_item="000250.SZ",
        notes="模拟程序重启时中断"
    )
    
    print("2. 模拟程序退出...")
    time.sleep(1)
    
    print("3. 模拟程序重新启动...")
    time.sleep(1)
    
    print("4. 检查恢复的进度...")
    progress = manager.get_script_progress("test_restart.py")
    if progress:
        print(f"   ✅ 成功恢复进度:")
        print(f"      状态: {progress['status']}")
        print(f"      进度: {progress['completed_items']}/{progress['total_items']}")
        print(f"      最后处理: {progress['last_processed_item']}")
        print(f"      时间: {progress['last_run_time']}")
    else:
        print("   ❌ 未能恢复进度")

def test_resume_detection():
    """测试断点续传检测"""
    print("\n=== 测试断点续传检测 ===")
    
    from gui.script_wrapper import ScriptWrapper
    
    # 测试几个脚本的断点续传检测
    test_scripts = ["daily.py", "daily_basic.py", "stock_basic.py"]
    
    for script_name in test_scripts:
        print(f"\n检测 {script_name}:")
        
        # 创建虚拟脚本路径
        script_path = f"market_data/{script_name}"
        wrapper = ScriptWrapper(script_path, script_name)
        
        can_resume, message = wrapper.check_resume_possibility()
        print(f"  可以断点续传: {can_resume}")
        print(f"  消息: {message}")
        
        if can_resume:
            resume_info = wrapper.get_resume_info()
            if resume_info:
                print(f"  进度: {resume_info['progress_percent']}%")
                print(f"  最后运行: {resume_info['last_run_time'][:19]}")

def cleanup_test_data():
    """清理测试数据"""
    print("\n=== 清理测试数据 ===")
    
    choice = input("是否清理所有测试数据? (y/n): ").lower()
    if choice == 'y':
        manager = ResumeManager()
        
        # 获取所有进度记录
        all_progress = manager.get_all_progress()
        for progress in all_progress:
            manager.clear_script_progress(progress['script_name'])
        
        print("✅ 已清理所有测试数据")
    else:
        print("❌ 取消清理操作")

def main():
    """主函数"""
    print("🔄 进度持久化功能测试")
    print("=" * 50)
    
    while True:
        print("\n选择测试项目:")
        print("1. 创建测试进度数据")
        print("2. 查看当前进度")
        print("3. 测试持久化功能")
        print("4. 模拟程序重启")
        print("5. 测试断点续传检测")
        print("6. 清理测试数据")
        print("7. 退出")
        
        choice = input("\n请选择 (1-7): ").strip()
        
        if choice == '1':
            create_test_progress()
        elif choice == '2':
            view_progress()
        elif choice == '3':
            test_persistence()
        elif choice == '4':
            simulate_program_restart()
        elif choice == '5':
            test_resume_detection()
        elif choice == '6':
            cleanup_test_data()
        elif choice == '7':
            print("退出测试程序")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
