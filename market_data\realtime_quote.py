import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)



# 添加项目根目录到Python路径

from config import get_token
import tushare as ts
import pandas as pd
import sqlite3
import time
from datetime import datetime
# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

class StockDatabase:
    def __init__(self, db_name='data.db'):
        """初始化数据库连接"""
        self.db_name = db_name
        self.conn = sqlite3.connect(db_name)
        print(f"数据库 {db_name} 连接成功")

    def create_table(self):
        """创建实时行情表"""
        create_table_sql = '''CREATE TABLE IF NOT EXISTS realtime_quote (
            name TEXT,
            ts_code TEXT,
            date TEXT,
            time TEXT,
            open REAL,
            pre_close REAL,
            price REAL,
            high REAL,
            low REAL,
            bid REAL,
            ask REAL,
            volume INTEGER,
            amount REAL,
            b1_v REAL,
            b1_p REAL,
            b2_v REAL,
            b2_p REAL,
            b3_v REAL,
            b3_p REAL,
            b4_v REAL,
            b4_p REAL,
            b5_v REAL,
            b5_p REAL,
            a1_v REAL,
            a1_p REAL,
            a2_v REAL,
            a2_p REAL,
            a3_v REAL,
            a3_p REAL,
            a4_v REAL,
            a4_p REAL,
            a5_v REAL,
            a5_p REAL,
            data_source TEXT,
            PRIMARY KEY (ts_code, date, time, data_source)
        )'''
        
        try:
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 realtime_quote 创建成功")
        except Exception as e:
            print(f"创建表格 realtime_quote 时出错: {e}")

    def check_data_exists(self, ts_code):
        """检查指定股票的数据是否已存在"""
        try:
            cursor = self.conn.cursor()
            cursor.execute('''
                SELECT COUNT(*) FROM realtime_quote
                WHERE ts_code = ?
            ''', (ts_code,))
            count = cursor.fetchone()[0]
            return count > 0
        except Exception as e:
            print(f"检查数据是否存在时出错: {e}")
            return False

    def insert_data(self, df, source, ts_code=None):
        """将DataFrame数据插入到表格中"""
        try:
            if df is None or df.empty:
                print("没有数据需要写入")
                return

            # 添加数据源信息
            df['data_source'] = source

            # 如果提供了ts_code，检查数据是否已存在
            if ts_code and self.check_data_exists(ts_code):
                print(f"数据已存在: {ts_code}，跳过更新")
                return

            # 尝试插入数据
            df.to_sql('realtime_quote', self.conn, if_exists='append', index=False)
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")

        except Exception as e:
            error_msg = str(e)
            if "UNIQUE constraint failed" in error_msg:
                print(f"数据已存在，跳过更新: {error_msg}")
            else:
                print(f"写入数据时出错: {e}")
                self.conn.rollback()

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_stock_list():
    """获取股票列表"""
    try:
        df = pro.stock_basic(exchange='', list_status='L', 
                            fields='ts_code,symbol,name,area,industry,list_date')
        return df
    except Exception as e:
        print(f"获取股票列表时出错: {e}")
        return None

def fetch_realtime_quote(ts_codes, source='sina'):
    """获取实时行情数据"""
    try:
        df = ts.realtime_quote(ts_code=ts_codes, src=source)
        # 添加延时以避免频率限制
        time.sleep(0.5)
        return df
    except Exception as e:
        print(f"获取实时行情数据时出错: {e}")
        return None

def process_stock_batch(stock_codes, db, source='sina'):
    """处理一批股票的实时行情数据"""
    if source == 'sina':
        # 新浪数据源支持批量查询，最多50个
        codes_str = ','.join(stock_codes)
        df = fetch_realtime_quote(codes_str, source)
        if df is not None and not df.empty:
            db.insert_data(df, source)
            print(f"成功获取 {len(stock_codes)} 只股票的实时行情数据")
    else:
        # 东方财富数据源只支持单个查询
        for code in stock_codes:
            df = fetch_realtime_quote(code, source)
            if df is not None and not df.empty:
                db.insert_data(df, source)
                print(f"成功获取 {code} 的实时行情数据")

def main():
    # 确保market_data目录存在
    os.makedirs('market_data', exist_ok=True)
    
    # 创建数据库实例
    db = StockDatabase()
    db.create_table()
    
    try:
        # 获取股票列表
        stock_list = get_stock_list()
        if stock_list is None:
            return
        
        # 设置数据源
        sources = ['sina', 'dc']
        
        total_stocks = len(stock_list)
        for source in sources:
            print(f"\n开始获取{source}数据源的实时行情...")
            
            if source == 'sina':
                # 新浪数据源：每次处理50只股票
                batch_size = 50
                for i in range(0, total_stocks, batch_size):
                    batch_stocks = stock_list['ts_code'].iloc[i:i+batch_size].tolist()
                    print(f"正在处理第 {i+1} 到 {min(i+batch_size, total_stocks)} 只股票...")
                    process_stock_batch(batch_stocks, db, source)
            else:
                # 东方财富数据源：每次处理1只股票
                for idx, row in stock_list.iterrows():
                    print(f"正在处理第 {idx+1}/{total_stocks} 只股票: {row['ts_code']}")
                    process_stock_batch([row['ts_code']], db, source)
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行A股实时行情数据获取程序...")
    main()
    print("程序运行完成！") 