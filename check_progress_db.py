#!/usr/bin/env python3
"""
检查进度数据库的内容
"""

import sqlite3
import os
from datetime import datetime

def check_progress_database():
    """检查进度数据库"""
    db_paths = [
        'gui/gui/progress.db',
        'gui/progress.db',
        'progress.db'
    ]
    
    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("❌ 未找到进度数据库文件")
        print("尝试的路径:")
        for path in db_paths:
            print(f"  - {path}")
        return False
    
    print(f"✅ 找到进度数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"📋 数据库中的表: {tables}")
        
        if 'script_progress' in tables:
            # 检查script_progress表的数据
            cursor.execute('SELECT COUNT(*) FROM script_progress')
            count = cursor.fetchone()[0]
            print(f"📊 script_progress表中有 {count} 条记录")
            
            if count > 0:
                cursor.execute('''
                    SELECT script_name, status, total_items, completed_items, 
                           failed_items, last_run_time 
                    FROM script_progress 
                    ORDER BY last_run_time DESC
                ''')
                rows = cursor.fetchall()
                
                print("\n📝 脚本进度记录:")
                print(f"{'脚本名称':<25} {'状态':<12} {'进度':<15} {'失败':<6} {'最后运行时间':<20}")
                print("-" * 85)
                
                for row in rows:
                    script_name, status, total, completed, failed, last_run = row
                    progress = f"{completed}/{total}" if total else "0/0"
                    last_run_str = last_run[:19] if last_run else "未知"
                    print(f"{script_name:<25} {status:<12} {progress:<15} {failed:<6} {last_run_str:<20}")
        else:
            print("❌ script_progress表不存在")
        
        if 'stock_progress' in tables:
            # 检查stock_progress表的数据
            cursor.execute('SELECT COUNT(*) FROM stock_progress')
            count = cursor.fetchone()[0]
            print(f"\n📈 stock_progress表中有 {count} 条记录")
            
            if count > 0:
                cursor.execute('''
                    SELECT script_name, COUNT(*) as stock_count
                    FROM stock_progress 
                    GROUP BY script_name
                    ORDER BY stock_count DESC
                ''')
                rows = cursor.fetchall()
                
                print("\n📊 各脚本的股票处理数量:")
                for script_name, stock_count in rows:
                    print(f"  {script_name}: {stock_count} 只股票")
        else:
            print("❌ stock_progress表不存在")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")
        return False

def create_test_data():
    """创建测试数据"""
    print("\n🔧 创建测试进度数据...")

    import sys
    import os

    # 确保能导入gui模块
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    try:
        from gui.resume_manager import ResumeManager

        manager = ResumeManager()
        print(f"📍 使用数据库路径: {manager.progress_db_path}")

        # 创建一些测试数据
        test_scripts = [
            {
                'name': 'daily.py',
                'status': 'completed',
                'total': 5413,
                'completed': 5413,
                'failed': 0
            },
            {
                'name': 'daily_basic.py',
                'status': 'completed',
                'total': 5413,
                'completed': 5200,
                'failed': 213
            },
            {
                'name': 'pro_bar.py',
                'status': 'interrupted',
                'total': 5413,
                'completed': 828,
                'failed': 15
            },
            {
                'name': 'adj_factor.py',
                'status': 'running',
                'total': 5413,
                'completed': 1500,
                'failed': 5
            },
            {
                'name': 'stock_basic.py',
                'status': 'completed',
                'total': 1,
                'completed': 1,
                'failed': 0
            }
        ]

        for script in test_scripts:
            manager.save_script_progress(
                script_name=script['name'],
                status=script['status'],
                total_items=script['total'],
                completed_items=script['completed'],
                failed_items=script['failed'],
                last_processed_item=f"00{script['completed']:04d}.SZ",
                notes=f"测试数据 - {script['status']}"
            )
            print(f"✅ 创建了 {script['name']} 的进度记录")

            # 为完成的脚本创建一些股票级别的进度
            if script['status'] == 'completed' and script['completed'] > 0:
                sample_count = min(script['completed'], 10)  # 创建最多10个样本
                for i in range(sample_count):
                    stock_code = f"{i+1:06d}.SZ"
                    manager.save_stock_progress(
                        script_name=script['name'],
                        ts_code=stock_code,
                        last_date="20241201",
                        status='completed',
                        record_count=100 + i
                    )

        print("✅ 测试数据创建完成")
        return True

    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 检查GUI进度显示问题")
    print("=" * 50)
    
    # 检查进度数据库
    if not check_progress_database():
        print("\n💡 数据库为空或不存在，是否创建测试数据？")
        choice = input("输入 'y' 创建测试数据: ").lower()
        if choice == 'y':
            if create_test_data():
                print("\n重新检查数据库:")
                check_progress_database()
    
    print("\n" + "=" * 50)
    print("🎯 解决方案:")
    print("1. 如果数据库为空，说明还没有运行过脚本")
    print("2. 运行一些数据获取脚本来生成进度记录")
    print("3. 重新启动GUI应用查看进度显示")
    print("4. 点击'查看进度'按钮查看详细信息")

if __name__ == "__main__":
    main()
