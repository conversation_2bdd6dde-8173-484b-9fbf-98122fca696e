#!/usr/bin/env python3
"""
改进GUI响应性的补丁
"""

import tkinter as tk
import threading
import time
import queue

class ResponsivenessImprover:
    """GUI响应性改进器"""
    
    def __init__(self, main_panel):
        self.main_panel = main_panel
        self.heartbeat_active = False
        
    def start_heartbeat(self):
        """启动心跳检测，确保GUI响应"""
        if not self.heartbeat_active:
            self.heartbeat_active = True
            self._heartbeat_loop()
    
    def stop_heartbeat(self):
        """停止心跳检测"""
        self.heartbeat_active = False
    
    def _heartbeat_loop(self):
        """心跳循环"""
        if self.heartbeat_active:
            # 强制更新GUI
            try:
                self.main_panel.root.update_idletasks()
            except:
                pass
            
            # 每50ms执行一次，提高响应性
            self.main_panel.root.after(50, self._heartbeat_loop)
    
    def add_progress_callback(self):
        """添加进度回调，定期更新界面"""
        def progress_update():
            if self.main_panel.thread_manager.is_running():
                # 更新界面状态
                self.main_panel.root.update_idletasks()
                # 继续监控
                self.main_panel.root.after(200, progress_update)
        
        progress_update()

def apply_responsiveness_improvements(main_panel):
    """应用响应性改进"""
    
    # 1. 减少消息处理间隔
    original_process_messages = main_panel.process_messages
    
    def improved_process_messages():
        """改进的消息处理"""
        try:
            # 处理更多消息，但限制处理时间
            start_time = time.time()
            message_count = 0
            
            while time.time() - start_time < 0.01:  # 最多处理10ms
                try:
                    message = main_panel.message_queue.get_nowait()
                    main_panel.handle_message(message)
                    message_count += 1
                    
                    if message_count >= 10:  # 最多处理10条消息
                        break
                        
                except queue.Empty:
                    break
                    
        except Exception as e:
            print(f"消息处理错误: {e}")
        finally:
            # 减少检查间隔到50ms
            main_panel.root.after(50, improved_process_messages)
    
    # 替换原有的消息处理方法
    main_panel.process_messages = improved_process_messages
    
    # 2. 添加强制更新
    def force_update():
        """强制更新GUI"""
        try:
            main_panel.root.update_idletasks()
        except:
            pass
        main_panel.root.after(100, force_update)
    
    force_update()
    
    # 3. 改进停止功能
    original_stop_all = main_panel.stop_all_scripts
    
    def improved_stop_all():
        """改进的停止所有脚本功能"""
        try:
            # 立即更新按钮状态
            main_panel.start_button.config(state='normal', text='开始执行')
            main_panel.stop_button.config(state='disabled')
            main_panel.stop_selected_button.config(state='disabled')
            
            # 强制更新界面
            main_panel.root.update_idletasks()
            
            # 执行原有停止逻辑
            original_stop_all()
            
            # 添加日志
            main_panel.add_log("INFO", "正在停止所有任务...")
            
        except Exception as e:
            main_panel.add_log("ERROR", f"停止任务时出错: {e}")
    
    main_panel.stop_all_scripts = improved_stop_all
    
    return True

def create_emergency_stop_window(main_panel):
    """创建紧急停止窗口"""
    
    def emergency_stop():
        """紧急停止功能"""
        try:
            # 强制停止所有线程
            main_panel.thread_manager.stop_all()
            
            # 重置界面状态
            main_panel.start_button.config(state='normal', text='开始执行')
            main_panel.stop_button.config(state='disabled')
            main_panel.stop_selected_button.config(state='disabled')
            
            # 清理进度
            for item_id in main_panel.tree.get_children():
                main_panel.tree.set(item_id, 'status', '已停止')
                main_panel.tree.set(item_id, 'progress', '0%')
            
            main_panel.add_log("WARNING", "已执行紧急停止")
            emergency_window.destroy()
            
        except Exception as e:
            main_panel.add_log("ERROR", f"紧急停止失败: {e}")
    
    # 创建紧急停止窗口
    emergency_window = tk.Toplevel(main_panel.root)
    emergency_window.title("紧急停止")
    emergency_window.geometry("300x150")
    emergency_window.transient(main_panel.root)
    emergency_window.grab_set()
    
    # 居中显示
    emergency_window.geometry("+%d+%d" % (
        main_panel.root.winfo_rootx() + 50,
        main_panel.root.winfo_rooty() + 50
    ))
    
    # 添加说明
    label = tk.Label(emergency_window, 
                    text="检测到程序可能无响应\n是否执行紧急停止？",
                    font=('Arial', 12))
    label.pack(pady=20)
    
    # 按钮框架
    button_frame = tk.Frame(emergency_window)
    button_frame.pack(pady=10)
    
    # 紧急停止按钮
    stop_btn = tk.Button(button_frame, 
                        text="紧急停止", 
                        command=emergency_stop,
                        bg='red', fg='white',
                        font=('Arial', 10, 'bold'))
    stop_btn.pack(side=tk.LEFT, padx=10)
    
    # 取消按钮
    cancel_btn = tk.Button(button_frame, 
                          text="取消", 
                          command=emergency_window.destroy)
    cancel_btn.pack(side=tk.LEFT, padx=10)

def main():
    """主函数 - 用于测试"""
    print("GUI响应性改进模块")
    print("使用方法:")
    print("1. 在main_panel.py中导入此模块")
    print("2. 调用apply_responsiveness_improvements(self)")
    print("3. 在需要时调用create_emergency_stop_window(self)")

if __name__ == "__main__":
    main()
