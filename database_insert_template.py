#!/usr/bin/env python3
"""
数据库插入方法模板 - 避免UNIQUE constraint错误
适用于所有需要处理重复数据的脚本
"""

def check_data_exists_template(table_name, primary_keys):
    """
    生成检查数据是否存在的方法模板
    
    Args:
        table_name: 表名
        primary_keys: 主键字段列表，如 ['ts_code', 'trade_date']
    """
    
    # 构建WHERE条件
    where_conditions = []
    for key in primary_keys:
        where_conditions.append(f"{key} = ?")
    where_clause = " AND ".join(where_conditions)
    
    return f'''
    def check_data_exists(self, {", ".join(primary_keys)}):
        """检查指定条件的数据是否已存在"""
        try:
            cursor = self.conn.cursor()
            cursor.execute(\'\'\'
                SELECT COUNT(*) FROM {table_name} 
                WHERE {where_clause}
            \'\'\', ({", ".join(primary_keys)}))
            count = cursor.fetchone()[0]
            return count > 0
        except Exception as e:
            print(f"检查数据是否存在时出错: {{e}}")
            return False
    '''

def insert_data_template(table_name, primary_keys):
    """
    生成插入数据方法模板
    
    Args:
        table_name: 表名
        primary_keys: 主键字段列表
    """
    
    # 构建参数列表
    check_params = ", ".join([f"df['{key}'].iloc[0]" if key != 'ts_code' else 'ts_code' for key in primary_keys])
    
    return f'''
    def insert_data(self, df, ts_code=None):
        """将DataFrame数据插入到表格中"""
        try:
            if df is None or df.empty:
                print("没有数据需要写入")
                return
            
            # 检查数据是否已存在（基于主键）
            if ts_code and len(df) > 0:
                # 检查第一条记录是否存在
                if self.check_data_exists({check_params}):
                    print(f"数据已存在: {{ts_code}}，跳过更新")
                    return
            
            # 尝试插入数据
            df.to_sql('{table_name}', self.conn, if_exists='append', index=False)
            self.conn.commit()
            print(f"成功写入 {{len(df)}} 条记录")
            
        except Exception as e:
            error_msg = str(e)
            if "UNIQUE constraint failed" in error_msg:
                print(f"数据已存在，跳过更新: {{error_msg}}")
            else:
                print(f"写入数据时出错: {{e}}")
                self.conn.rollback()
    '''

def generate_complete_solution():
    """生成完整的解决方案示例"""
    
    examples = {
        'adj_factor': {
            'table_name': 'adj_factor',
            'primary_keys': ['ts_code', 'trade_date'],
            'description': '复权因子表'
        },
        'daily': {
            'table_name': 'daily',
            'primary_keys': ['ts_code', 'trade_date'],
            'description': '日线数据表'
        },
        'weekly': {
            'table_name': 'weekly', 
            'primary_keys': ['ts_code', 'trade_date'],
            'description': '周线数据表'
        },
        'monthly': {
            'table_name': 'monthly',
            'primary_keys': ['ts_code', 'trade_date'],
            'description': '月线数据表'
        }
    }
    
    print("=" * 60)
    print("数据库插入方法解决方案")
    print("=" * 60)
    
    for key, config in examples.items():
        print(f"\n{config['description']} ({config['table_name']}):")
        print("-" * 40)
        
        # 生成检查方法
        check_method = check_data_exists_template(config['table_name'], config['primary_keys'])
        print("检查数据是否存在的方法:")
        print(check_method)
        
        # 生成插入方法
        insert_method = insert_data_template(config['table_name'], config['primary_keys'])
        print("插入数据的方法:")
        print(insert_method)

def main():
    """主函数"""
    print("数据库插入方法模板生成器")
    print("用于解决UNIQUE constraint failed错误")
    
    generate_complete_solution()
    
    print("\n" + "=" * 60)
    print("使用说明")
    print("=" * 60)
    print("1. 复制相应的方法模板到您的脚本中")
    print("2. 替换原有的insert_data方法")
    print("3. 在调用insert_data时传入ts_code参数")
    print("4. 脚本会自动检查数据是否存在，避免重复插入")
    
    print("\n优点:")
    print("✓ 避免UNIQUE constraint错误")
    print("✓ 减少数据库写入负担")
    print("✓ 保持数据完整性")
    print("✓ 提供清晰的日志信息")
    
    print("\n示例输出:")
    print("- 数据已存在: 000001.SZ (20240101 到 20241201)，跳过更新")
    print("- 数据已存在，跳过更新: UNIQUE constraint failed...")
    print("- 成功写入 725 条记录")

if __name__ == "__main__":
    main()
