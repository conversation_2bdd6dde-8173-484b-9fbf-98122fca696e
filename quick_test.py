#!/usr/bin/env python3
"""
快速测试脚本 - 验证GUI修复是否有效
"""

import os
import sys

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_thread_manager_import():
    """测试线程管理器导入"""
    try:
        from gui.thread_manager import ThreadManager
        print("✓ ThreadManager 导入成功")
        
        # 测试创建实例
        manager = ThreadManager()
        print("✓ ThreadManager 实例创建成功")
        
        return True
    except Exception as e:
        print(f"✗ ThreadManager 测试失败: {e}")
        return False

def test_script_manager():
    """测试脚本管理器"""
    try:
        from gui.script_manager import ScriptManager
        manager = ScriptManager()
        
        scripts = manager.get_all_scripts()
        print(f"✓ 发现 {len(scripts)} 个脚本")
        
        if len(scripts) > 0:
            print("前5个脚本:")
            for i, script in enumerate(scripts[:5]):
                print(f"  {i+1}. {script['name']}")
        
        return True
    except Exception as e:
        print(f"✗ ScriptManager 测试失败: {e}")
        return False

def test_main_panel_import():
    """测试主面板导入"""
    try:
        from gui.main_panel import StockDataGUIPanel
        print("✓ StockDataGUIPanel 导入成功")
        return True
    except Exception as e:
        print(f"✗ StockDataGUIPanel 导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 40)
    print("GUI修复验证测试")
    print("=" * 40)
    
    tests = [
        ("线程管理器", test_thread_manager_import),
        ("脚本管理器", test_script_manager),
        ("主面板", test_main_panel_import)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n测试 {test_name}...")
        if test_func():
            passed += 1
    
    print(f"\n结果: {passed}/{len(tests)} 测试通过")
    
    if passed == len(tests):
        print("\n🎉 所有测试通过！修复成功。")
        print("现在可以正常使用GUI了:")
        print("  python start_gui.py")
    else:
        print("\n❌ 部分测试失败，请检查错误信息。")

if __name__ == "__main__":
    main()
