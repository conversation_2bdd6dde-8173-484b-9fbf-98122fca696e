import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)



# 添加项目根目录到Python路径

from config import get_token
import tushare as ts
import pandas as pd
import sqlite3
import time
from datetime import datetime, timedelta
# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

class StockDatabase:
    def __init__(self, db_name='data.db'):
        """初始化数据库连接"""
        self.db_name = db_name
        self.conn = sqlite3.connect(db_name)
        print(f"数据库 {db_name} 连接成功")

    def create_table(self):
        """创建大宗交易表"""
        try:
            create_table_sql = '''CREATE TABLE IF NOT EXISTS block_trade (
                ts_code TEXT,
                trade_date TEXT,
                price FLOAT,
                vol FLOAT,
                amount FLOAT,
                buyer TEXT,
                seller TEXT,
                PRIMARY KEY (ts_code, trade_date, price, vol, buyer, seller)
            )'''
            
            # 首先删除已存在的表
            self.conn.execute('DROP TABLE IF EXISTS block_trade')
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 block_trade 创建成功")
            
        except Exception as e:
            print(f"创建表格 block_trade 时出错: {e}")
            raise

    def insert_data(self, df):
        """将DataFrame数据插入到表格中"""
        try:
            # 创建临时表
            temp_table_name = 'temp_block_trade'
            df.to_sql(temp_table_name, self.conn, if_exists='replace', index=False)
            
            # 使用INSERT OR REPLACE将数据从临时表插入到主表
            insert_sql = f'''
                INSERT OR REPLACE INTO block_trade 
                SELECT * FROM {temp_table_name}
            '''
            self.conn.execute(insert_sql)
            
            # 删除临时表
            self.conn.execute(f'DROP TABLE IF EXISTS {temp_table_name}')
            
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")
        except Exception as e:
            print(f"写入数据时出错: {e}")
            self.conn.rollback()

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_date_ranges():
    """获取近5年的日期范围，按月划分"""
    ranges = []
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3*365)  # 3年前
    
    # 将时间范围按月划分，每次获取一个月的数据
    current_date = end_date
    while current_date > start_date:
        month_end = current_date.strftime('%Y%m%d')
        current_date = current_date - timedelta(days=30)  # 大约一个月
        month_start = current_date.strftime('%Y%m%d')
        ranges.append((month_start, month_end))
    
    return ranges

def fetch_block_trade_data(start_date, end_date):
    """获取指定日期范围的大宗交易数据"""
    try:
        print(f"正在获取 {start_date} 至 {end_date} 的大宗交易数据...")
        df = pro.block_trade(start_date=start_date, end_date=end_date)
        time.sleep(0.5)  # 添加延时以避免频率限制
        return df
    except Exception as e:
        print(f"获取 {start_date} 至 {end_date} 的大宗交易数据时出错: {e}")
        return None

def process_period_data(start_date, end_date, db):
    """处理指定时期的数据"""
    try:
        df = fetch_block_trade_data(start_date, end_date)
        if df is not None and not df.empty:
            db.insert_data(df)
            print(f"成功获取 {start_date} 至 {end_date} 的大宗交易数据，共 {len(df)} 条记录")
            return True
        return False
    except Exception as e:
        print(f"处理 {start_date} 至 {end_date} 的大宗交易数据时出错: {e}")
        return False

def main():
    # 确保reference_data目录存在
    os.makedirs('reference_data', exist_ok=True)
    
    # 创建数据库实例
    db = StockDatabase()
    
    try:
        # 创建表
        db.create_table()
        
        # 获取日期范围列表
        date_ranges = get_date_ranges()
        print(f"将获取以下日期范围的数据: {date_ranges}")
        
        total_periods = len(date_ranges)
        for period_idx, (start_date, end_date) in enumerate(date_ranges):
            print(f"正在处理第 {period_idx+1}/{total_periods} 个时期: {start_date} 至 {end_date}")
            
            if not process_period_data(start_date, end_date, db):
                print(f"跳过 {start_date} 至 {end_date} 的处理")
                continue
            
            # 每处理4个月后暂停15秒
            if (period_idx + 1) % 4 == 0:
                print(f"已处理 {period_idx+1} 个月，暂停15秒...")
                time.sleep(15)
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行A股大宗交易数据获取程序...")
    main()
    print("程序运行完成！") 