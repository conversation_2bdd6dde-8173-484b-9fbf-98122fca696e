import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)



# 添加项目根目录到Python路径

from config import get_token
import tushare as ts
import pandas as pd
import sqlite3
import time
from datetime import datetime, timedelta
# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

class StockDatabase:
    def __init__(self, db_name='data.db'):
        """初始化数据库连接"""
        self.db_name = db_name
        self.conn = sqlite3.connect(db_name)
        print(f"数据库 {db_name} 连接成功")

    def create_table(self):
        """创建资产负债表"""
        # 首先获取一个季度的数据来确定实际的列
        try:
            sample_df = pro.balancesheet_vip(period='20230930')
            if sample_df is None or sample_df.empty:
                raise Exception("无法获取样本数据来创建表结构")
            
            # 获取实际的列名
            columns = sample_df.columns.tolist()
            
            # 构建创建表的SQL语句
            columns_sql = []
            for col in columns:
                if col in ['ts_code', 'ann_date', 'f_ann_date', 'end_date', 'report_type', 'comp_type', 'end_type', 'update_flag']:
                    columns_sql.append(f"{col} TEXT")
                else:
                    columns_sql.append(f"{col} REAL")
            
            # 添加主键约束
            columns_sql.append("PRIMARY KEY (ts_code, end_date, report_type)")
            
            # 首先删除已存在的表
            drop_table_sql = 'DROP TABLE IF EXISTS balancesheet_vip'
            create_table_sql = f'''CREATE TABLE IF NOT EXISTS balancesheet_vip (
                {','.join(columns_sql)}
            )'''
            
            self.conn.execute(drop_table_sql)
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 balancesheet_vip 创建成功")
            
            # 保存列信息以供后续使用
            self.columns = columns
            
        except Exception as e:
            print(f"创建表格 balancesheet_vip 时出错: {e}")
            raise

    def insert_data(self, df):
        """将DataFrame数据插入到表格中"""
        try:
            # 确保DataFrame的列与表结构匹配
            df = df[self.columns]
            
            # 创建临时表
            temp_table_name = 'temp_balancesheet_vip'
            df.to_sql(temp_table_name, self.conn, if_exists='replace', index=False)
            
            # 使用INSERT OR REPLACE将数据从临时表插入到主表
            insert_sql = f'''
                INSERT OR REPLACE INTO balancesheet_vip 
                SELECT * FROM {temp_table_name}
            '''
            self.conn.execute(insert_sql)
            
            # 删除临时表
            self.conn.execute(f'DROP TABLE IF EXISTS {temp_table_name}')
            
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")
        except Exception as e:
            print(f"写入数据时出错: {e}")
            self.conn.rollback()

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_report_periods():
    """获取近5年的报告期"""
    periods = []
    now = datetime.now()
    
    # 获取最近12个季度（3年）的报告期
    for i in range(12):
        year = now.year - (i // 4)
        quarter = 4 - (i % 4)
        if quarter == 4:
            period = f"{year}1231"
        elif quarter == 3:
            period = f"{year}0930"
        elif quarter == 2:
            period = f"{year}0630"
        else:
            period = f"{year}0331"
        periods.append(period)
    
    return sorted(periods)

def fetch_balancesheet_data(period):
    """获取单个报告期的资产负债表数据"""
    try:
        print(f"正在获取 {period} 的资产负债表数据...")
        df = pro.balancesheet_vip(period=period)
        time.sleep(0.5)  # 添加延时以避免频率限制
        return df
    except Exception as e:
        print(f"获取 {period} 的资产负债表数据时出错: {e}")
        return None

def process_period_data(period, db):
    """处理单个报告期的数据"""
    try:
        df = fetch_balancesheet_data(period)
        if df is not None and not df.empty:
            db.insert_data(df)
            print(f"成功获取 {period} 的资产负债表数据，共 {len(df)} 条记录")
            return True
        return False
    except Exception as e:
        print(f"处理 {period} 的资产负债表数据时出错: {e}")
        return False

def main():
    # 确保financial_data目录存在
    os.makedirs('financial_data', exist_ok=True)
    
    # 创建数据库实例
    db = StockDatabase()
    
    try:
        # 创建表
        db.create_table()
        
        # 获取报告期列表
        periods = get_report_periods()
        print(f"将获取以下报告期的数据: {periods}")
        
        total_periods = len(periods)
        for idx, period in enumerate(periods):
            print(f"正在处理第 {idx+1}/{total_periods} 个报告期: {period}")
            
            if not process_period_data(period, db):
                print(f"跳过 {period} 的处理")
                continue
            
            # 每处理4个报告期（一年）后额外等待一段时间
            if (idx + 1) % 4 == 0:
                print(f"已处理 {idx+1} 个报告期，暂停1分钟...")
                time.sleep(60)
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行A股资产负债表数据获取程序...")
    main()
    print("程序运行完成！") 