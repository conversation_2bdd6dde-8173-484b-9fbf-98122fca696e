import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)



# 添加项目根目录到Python路径

from config import get_token
import tushare as ts
import pandas as pd
import sqlite3
import time
from datetime import datetime, timedelta
# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

class StockDatabase:
    def __init__(self, db_name='data.db'):
        """初始化数据库连接"""
        self.db_name = db_name
        self.conn = sqlite3.connect(db_name)
        print(f"数据库 {db_name} 连接成功")

    def create_table(self):
        """创建行情数据表"""
        # 首先删除已存在的表
        drop_table_sql = 'DROP TABLE IF EXISTS pro_bar'
        create_table_sql = '''CREATE TABLE IF NOT EXISTS pro_bar (
            ts_code TEXT,
            trade_date TEXT,
            open REAL,
            high REAL,
            low REAL,
            close REAL,
            pre_close REAL,
            change REAL,
            pct_chg REAL,
            vol REAL,
            amount REAL,
            adj_factor REAL,
            PRIMARY KEY (ts_code, trade_date)
        )'''
        
        try:
            self.conn.execute(drop_table_sql)
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 pro_bar 创建成功")
        except Exception as e:
            print(f"创建表格 pro_bar 时出错: {e}")

    def insert_data(self, df):
        """将DataFrame数据插入到表格中"""
        try:
            # 将DataFrame写入SQLite数据库
            df.to_sql('pro_bar', self.conn, if_exists='append', index=False)
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")
        except Exception as e:
            print(f"写入数据时出错: {e}")
            # 如果是因为表结构问题，尝试重新创建表并插入数据
            if "no column named" in str(e):
                print("检测到表结构不匹配，尝试重新创建表...")
                self.create_table()
                df.to_sql('pro_bar', self.conn, if_exists='append', index=False)
                self.conn.commit()
                print(f"重新创建表后成功写入 {len(df)} 条记录")

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_stock_list():
    """获取股票列表"""
    try:
        df = pro.stock_basic(exchange='', list_status='L', 
                            fields='ts_code,symbol,name,area,industry,list_date')
        return df
    except Exception as e:
        print(f"获取股票列表时出错: {e}")
        return None

def get_date_range():
    """获取近5年的日期范围"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3*365)
    return start_date.strftime('%Y%m%d'), end_date.strftime('%Y%m%d')

def fetch_stock_data(ts_code, start_date, end_date):
    """获取单个股票的行情数据"""
    try:
        df = ts.pro_bar(
            ts_code=ts_code,
            asset='E',  # 只获取股票数据
            adj='qfq',  # 前复权
            freq='D',   # 日线数据
            start_date=start_date,
            end_date=end_date,
            adjfactor=True  # 包含复权因子
        )
        time.sleep(0.5)  # 添加延时以避免频率限制
        return df
    except Exception as e:
        print(f"获取 {ts_code} 的行情数据时出错: {e}")
        return None

def process_stock_data(ts_code, start_date, end_date, db):
    """处理单个股票的数据"""
    try:
        print(f"正在获取 {ts_code} 的行情数据...")
        df = fetch_stock_data(ts_code, start_date, end_date)
        if df is not None and not df.empty:
            # 确保数据框包含所有必要的列
            required_columns = ['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 
                              'pre_close', 'change', 'pct_chg', 'vol', 'amount', 'adj_factor']
            for col in required_columns:
                if col not in df.columns:
                    df[col] = None
            
            # 只保留需要的列，并按照表结构的顺序排列
            df = df[required_columns]
            
            db.insert_data(df)
            print(f"成功获取 {ts_code} 的行情数据，共 {len(df)} 条记录")
        return True
    except Exception as e:
        print(f"处理 {ts_code} 的行情数据时出错: {e}")
        return False

def main():
    # 确保market_data目录存在
    os.makedirs('market_data', exist_ok=True)
    
    # 创建数据库实例
    db = StockDatabase()
    db.create_table()
    
    try:
        # 获取股票列表
        stock_list = get_stock_list()
        if stock_list is None:
            return
        
        # 获取日期范围
        start_date, end_date = get_date_range()
        print(f"获取数据的日期范围: {start_date} 至 {end_date}")
        
        total_stocks = len(stock_list)
        for idx, row in stock_list.iterrows():
            ts_code = row['ts_code']
            print(f"正在处理第 {idx+1}/{total_stocks} 只股票: {ts_code}")
            
            if not process_stock_data(ts_code, start_date, end_date, db):
                print(f"跳过 {ts_code} 的处理")
                continue
            
            # 每处理50只股票后额外等待一段时间
            if (idx + 1) % 50 == 0:
                print(f"已处理 {idx+1} 只股票，暂停1分钟...")
                time.sleep(60)
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行A股行情数据获取程序...")
    main()
    print("程序运行完成！") 