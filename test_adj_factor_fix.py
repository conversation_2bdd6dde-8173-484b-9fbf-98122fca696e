#!/usr/bin/env python3
"""
测试修复后的adj_factor.py脚本
验证是否正确处理重复数据
"""

import os
import sys
import sqlite3
import pandas as pd
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_adj_factor_database():
    """测试adj_factor数据库操作"""
    print("=" * 60)
    print("测试adj_factor数据库修复")
    print("=" * 60)
    
    try:
        # 导入修复后的模块
        sys.path.insert(0, os.path.join(current_dir, 'market_data'))
        from adj_factor import StockDatabase
        
        # 创建测试数据库实例
        db = StockDatabase('test_adj_factor.db')
        db.create_table()
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'ts_code': ['000001.SZ', '000001.SZ', '000002.SZ'],
            'trade_date': ['20241201', '20241202', '20241201'],
            'adj_factor': [1.0, 1.1, 0.9]
        })
        
        print("第一次插入测试数据...")
        db.insert_data(test_data, '000001.SZ')
        
        print("\n第二次插入相同数据（应该跳过）...")
        db.insert_data(test_data, '000001.SZ')
        
        # 检查数据库中的数据
        conn = sqlite3.connect('test_adj_factor.db')
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM adj_factor")
        results = cursor.fetchall()
        
        print(f"\n数据库中的记录数: {len(results)}")
        for row in results:
            print(f"  {row}")
        
        conn.close()
        db.close()
        
        # 清理测试文件
        if os.path.exists('test_adj_factor.db'):
            os.remove('test_adj_factor.db')
            print("\n测试数据库文件已清理")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def check_existing_data():
    """检查现有数据库中的adj_factor数据"""
    print("\n" + "=" * 60)
    print("检查现有adj_factor数据")
    print("=" * 60)
    
    db_path = 'data.db'
    if not os.path.exists(db_path):
        print("主数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='adj_factor'")
        if not cursor.fetchone():
            print("adj_factor表不存在")
            return
        
        # 获取基本统计信息
        cursor.execute("SELECT COUNT(*) FROM adj_factor")
        total_records = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT ts_code) FROM adj_factor")
        unique_stocks = cursor.fetchone()[0]
        
        cursor.execute("SELECT MIN(trade_date), MAX(trade_date) FROM adj_factor")
        date_range = cursor.fetchone()
        
        print(f"总记录数: {total_records:,}")
        print(f"股票数量: {unique_stocks:,}")
        print(f"日期范围: {date_range[0]} 到 {date_range[1]}")
        
        # 检查是否有重复数据
        cursor.execute('''
            SELECT ts_code, trade_date, COUNT(*) as cnt 
            FROM adj_factor 
            GROUP BY ts_code, trade_date 
            HAVING COUNT(*) > 1 
            LIMIT 5
        ''')
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"\n发现重复数据:")
            for dup in duplicates:
                print(f"  {dup[0]} {dup[1]}: {dup[2]} 条重复记录")
        else:
            print("\n✓ 没有发现重复数据")
        
        # 检查最近的数据
        cursor.execute('''
            SELECT ts_code, trade_date, adj_factor 
            FROM adj_factor 
            ORDER BY trade_date DESC 
            LIMIT 5
        ''')
        recent_data = cursor.fetchall()
        
        print(f"\n最近的5条记录:")
        for row in recent_data:
            print(f"  {row[0]} {row[1]}: {row[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据时出错: {e}")

def simulate_duplicate_insert():
    """模拟重复插入的情况"""
    print("\n" + "=" * 60)
    print("模拟重复插入测试")
    print("=" * 60)
    
    print("模拟场景:")
    print("1. 脚本第一次运行，成功插入数据")
    print("2. 脚本第二次运行，遇到相同数据")
    print("3. 修复后的脚本应该跳过重复数据")
    
    print("\n预期行为:")
    print("✓ 第一次: 成功写入 X 条记录")
    print("✓ 第二次: 数据已存在: 000001.SZ，跳过更新")
    print("✓ 避免: UNIQUE constraint failed 错误")

def main():
    """主测试函数"""
    print("adj_factor脚本修复验证")
    
    # 执行测试
    tests = [
        ("数据库操作测试", test_adj_factor_database),
        ("现有数据检查", check_existing_data),
        ("重复插入模拟", simulate_duplicate_insert)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n执行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 出错: {e}")
    
    print(f"\n测试结果: {passed}/{len(tests)} 通过")
    
    print("\n" + "=" * 60)
    print("修复总结")
    print("=" * 60)
    print("✅ 已修复 market_data/adj_factor.py")
    print("✅ 添加数据存在性检查")
    print("✅ 优雅处理UNIQUE constraint错误")
    print("✅ 减少不必要的数据库操作")
    
    print("\n现在运行adj_factor脚本时:")
    print("- 如果数据已存在: 显示'数据已存在，跳过更新'")
    print("- 如果数据不存在: 正常插入新数据")
    print("- 不再出现: UNIQUE constraint failed 错误")

if __name__ == "__main__":
    main()
