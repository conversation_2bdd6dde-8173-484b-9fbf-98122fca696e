#!/usr/bin/env python3
"""
警告抑制器 - 过滤tushare库的FutureWarning警告
"""

import warnings
import sys

def suppress_tushare_warnings():
    """抑制tushare相关的FutureWarning警告"""
    
    # 过滤特定的FutureWarning
    warnings.filterwarnings(
        'ignore',
        category=FutureWarning,
        message=".*fillna with 'method' is deprecated.*"
    )
    
    # 过滤所有来自tushare的FutureWarning
    warnings.filterwarnings(
        'ignore',
        category=FutureWarning,
        module='tushare.*'
    )
    
    print("✅ 已启用tushare警告过滤器")

def suppress_all_future_warnings():
    """抑制所有FutureWarning警告"""
    warnings.filterwarnings('ignore', category=FutureWarning)
    print("⚠️ 已抑制所有FutureWarning警告")

def restore_warnings():
    """恢复所有警告显示"""
    warnings.resetwarnings()
    print("🔄 已恢复所有警告显示")

def configure_warnings_for_production():
    """为生产环境配置警告设置"""
    
    # 只显示错误和严重警告
    warnings.filterwarnings('ignore', category=FutureWarning)
    warnings.filterwarnings('ignore', category=DeprecationWarning)
    warnings.filterwarnings('ignore', category=PendingDeprecationWarning)
    
    # 保留重要的警告
    warnings.filterwarnings('default', category=UserWarning)
    warnings.filterwarnings('default', category=RuntimeWarning)
    
    print("🎯 已配置生产环境警告设置")

def show_warning_info():
    """显示当前警告配置信息"""
    print("\n📋 当前警告过滤器配置:")
    
    if warnings.filters:
        for i, filter_item in enumerate(warnings.filters, 1):
            action, message, category, module, lineno = filter_item
            print(f"  {i}. {action}: {category.__name__ if category else 'All'}")
            if module:
                print(f"     模块: {module}")
            if message:
                print(f"     消息: {message}")
    else:
        print("  无活动的警告过滤器")

def main():
    """主函数 - 提供交互式警告管理"""
    print("🔧 警告管理工具")
    print("=" * 40)
    
    while True:
        print("\n选择操作:")
        print("1. 抑制tushare警告")
        print("2. 抑制所有FutureWarning")
        print("3. 恢复所有警告")
        print("4. 生产环境配置")
        print("5. 查看当前配置")
        print("6. 退出")
        
        choice = input("\n请选择 (1-6): ").strip()
        
        if choice == '1':
            suppress_tushare_warnings()
        elif choice == '2':
            suppress_all_future_warnings()
        elif choice == '3':
            restore_warnings()
        elif choice == '4':
            configure_warnings_for_production()
        elif choice == '5':
            show_warning_info()
        elif choice == '6':
            print("退出警告管理工具")
            break
        else:
            print("无效选择，请重新输入")

# 自动应用推荐设置
def auto_configure():
    """自动应用推荐的警告配置"""
    suppress_tushare_warnings()

if __name__ == "__main__":
    main()
else:
    # 当作为模块导入时，自动配置
    auto_configure()
