import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)



# 添加项目根目录到Python路径

from config import get_token
import tushare as ts
import pandas as pd
import sqlite3
import time
from datetime import datetime, timedelta
# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

class StockDatabase:
    def __init__(self, db_name='data.db'):
        """初始化数据库连接"""
        self.db_name = db_name
        self.conn = sqlite3.connect(db_name)
        print(f"数据库 {db_name} 连接成功")

    def create_table(self):
        """创建利润表"""
        # 首先删除已存在的表
        drop_table_sql = 'DROP TABLE IF EXISTS income_vip'
        create_table_sql = '''CREATE TABLE IF NOT EXISTS income_vip (
            ts_code TEXT,
            ann_date TEXT,
            f_ann_date TEXT,
            end_date TEXT,
            report_type TEXT,
            comp_type TEXT,
            end_type TEXT,
            basic_eps REAL,
            diluted_eps REAL,
            total_revenue REAL,
            revenue REAL,
            int_income REAL,
            prem_earned REAL,
            comm_income REAL,
            n_commis_income REAL,
            n_oth_income REAL,
            n_oth_b_income REAL,
            prem_income REAL,
            out_prem REAL,
            une_prem_reser REAL,
            reins_income REAL,
            n_sec_tb_income REAL,
            n_sec_uw_income REAL,
            n_asset_mg_income REAL,
            oth_b_income REAL,
            fv_value_chg_gain REAL,
            invest_income REAL,
            ass_invest_income REAL,
            forex_gain REAL,
            total_cogs REAL,
            oper_cost REAL,
            int_exp REAL,
            comm_exp REAL,
            biz_tax_surchg REAL,
            sell_exp REAL,
            admin_exp REAL,
            fin_exp REAL,
            assets_impair_loss REAL,
            prem_refund REAL,
            compens_payout REAL,
            reser_insur_liab REAL,
            div_payt REAL,
            reins_exp REAL,
            oper_exp REAL,
            compens_payout_refu REAL,
            insur_reser_refu REAL,
            reins_cost_refund REAL,
            other_bus_cost REAL,
            operate_profit REAL,
            non_oper_income REAL,
            non_oper_exp REAL,
            nca_disploss REAL,
            total_profit REAL,
            income_tax REAL,
            n_income REAL,
            n_income_attr_p REAL,
            minority_gain REAL,
            oth_compr_income REAL,
            t_compr_income REAL,
            compr_inc_attr_p REAL,
            compr_inc_attr_m_s REAL,
            ebit REAL,
            ebitda REAL,
            insurance_exp REAL,
            undist_profit REAL,
            distable_profit REAL,
            rd_exp REAL,
            fin_exp_int_exp REAL,
            fin_exp_int_inc REAL,
            transfer_surplus_rese REAL,
            transfer_housing_imprest REAL,
            transfer_oth REAL,
            adj_lossgain REAL,
            withdra_legal_surplus REAL,
            withdra_legal_pubfund REAL,
            withdra_biz_devfund REAL,
            withdra_rese_fund REAL,
            withdra_oth_ersu REAL,
            workers_welfare REAL,
            distr_profit_shrhder REAL,
            prfshare_payable_dvd REAL,
            comshare_payable_dvd REAL,
            capit_comstock_div REAL,
            update_flag TEXT,
            PRIMARY KEY (ts_code, end_date, report_type)
        )'''
        
        try:
            self.conn.execute(drop_table_sql)
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 income_vip 创建成功")
        except Exception as e:
            print(f"创建表格 income_vip 时出错: {e}")

    def insert_data(self, df):
        """将DataFrame数据插入到表格中"""
        try:
            # 创建临时表
            temp_table_name = 'temp_income_vip'
            df.to_sql(temp_table_name, self.conn, if_exists='replace', index=False)
            
            # 使用INSERT OR REPLACE将数据从临时表插入到主表
            insert_sql = f'''
                INSERT OR REPLACE INTO income_vip 
                SELECT * FROM {temp_table_name}
            '''
            self.conn.execute(insert_sql)
            
            # 删除临时表
            self.conn.execute(f'DROP TABLE IF EXISTS {temp_table_name}')
            
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")
        except Exception as e:
            print(f"写入数据时出错: {e}")
            self.conn.rollback()

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_stock_list():
    """获取股票列表"""
    try:
        df = pro.stock_basic(exchange='', list_status='L', 
                            fields='ts_code,symbol,name,area,industry,list_date')
        return df
    except Exception as e:
        print(f"获取股票列表时出错: {e}")
        return None

def get_report_periods():
    """获取近5年的报告期"""
    periods = []
    now = datetime.now()
    
    # 获取最近12个季度（3年）的报告期
    for i in range(12):
        year = now.year - (i // 4)
        quarter = 4 - (i % 4)
        if quarter == 4:
            period = f"{year}1231"
        elif quarter == 3:
            period = f"{year}0930"
        elif quarter == 2:
            period = f"{year}0630"
        else:
            period = f"{year}0331"
        periods.append(period)
    
    return sorted(periods)

def fetch_income_data(period):
    """获取单个报告期的利润表数据"""
    try:
        print(f"正在获取 {period} 的利润表数据...")
        df = pro.income_vip(period=period)
        if df is not None and not df.empty:
            # 确保所有必需的列都存在
            required_columns = ['ts_code', 'ann_date', 'f_ann_date', 'end_date', 'report_type', 
                              'comp_type', 'end_type', 'basic_eps', 'diluted_eps', 'total_revenue',
                              'revenue', 'int_income', 'prem_earned', 'comm_income', 'n_commis_income',
                              'n_oth_income', 'n_oth_b_income', 'prem_income', 'out_prem', 'une_prem_reser',
                              'reins_income', 'n_sec_tb_income', 'n_sec_uw_income', 'n_asset_mg_income',
                              'oth_b_income', 'fv_value_chg_gain', 'invest_income', 'ass_invest_income',
                              'forex_gain', 'total_cogs', 'oper_cost', 'int_exp', 'comm_exp', 'biz_tax_surchg',
                              'sell_exp', 'admin_exp', 'fin_exp', 'assets_impair_loss', 'prem_refund',
                              'compens_payout', 'reser_insur_liab', 'div_payt', 'reins_exp', 'oper_exp',
                              'compens_payout_refu', 'insur_reser_refu', 'reins_cost_refund', 'other_bus_cost',
                              'operate_profit', 'non_oper_income', 'non_oper_exp', 'nca_disploss', 'total_profit',
                              'income_tax', 'n_income', 'n_income_attr_p', 'minority_gain', 'oth_compr_income',
                              't_compr_income', 'compr_inc_attr_p', 'compr_inc_attr_m_s', 'ebit', 'ebitda',
                              'insurance_exp', 'undist_profit', 'distable_profit', 'rd_exp', 'fin_exp_int_exp',
                              'fin_exp_int_inc', 'transfer_surplus_rese', 'transfer_housing_imprest', 'transfer_oth',
                              'adj_lossgain', 'withdra_legal_surplus', 'withdra_legal_pubfund', 'withdra_biz_devfund',
                              'withdra_rese_fund', 'withdra_oth_ersu', 'workers_welfare', 'distr_profit_shrhder',
                              'prfshare_payable_dvd', 'comshare_payable_dvd', 'capit_comstock_div', 'update_flag']
            
            # 为缺失的列添加空值
            for col in required_columns:
                if col not in df.columns:
                    df[col] = None
            
            # 只保留我们需要的列，并按照指定顺序排列
            df = df[required_columns]
        
        time.sleep(0.5)  # 添加延时以避免频率限制
        return df
    except Exception as e:
        print(f"获取 {period} 的利润表数据时出错: {e}")
        return None

def process_period_data(period, db):
    """处理单个报告期的数据"""
    df = fetch_income_data(period)
    if df is not None and not df.empty:
        print(f"获取到 {len(df)} 条记录")
        db.insert_data(df)
    else:
        print(f"未获取到 {period} 的数据")

def main():
    """主函数"""
    # 确保financial_data目录存在
    os.makedirs('financial_data', exist_ok=True)
    
    # 创建数据库连接
    db = StockDatabase('data.db')
    
    try:
        # 创建表
        db.create_table()
        
        # 获取报告期
        periods = get_report_periods()
        
        # 处理每个报告期的数据
        for period in periods:
            process_period_data(period, db)
            
    except Exception as e:
        print(f"处理数据时出错: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    main() 