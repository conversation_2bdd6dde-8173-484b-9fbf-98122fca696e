#!/usr/bin/env python3
"""
测试断点续传功能的示例脚本
"""

import os
import sys
import time
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from gui.resume_manager import ResumeManager
from gui.script_wrapper import ScriptWrapper

def simulate_data_fetch():
    """模拟数据获取过程"""
    resume_manager = ResumeManager()
    script_name = "test_script.py"
    
    # 模拟股票列表
    stock_list = [f"{i:06d}.SZ" for i in range(1, 101)]  # 100只股票
    
    print(f"开始模拟数据获取: {script_name}")
    print(f"总共需要处理 {len(stock_list)} 只股票")
    
    # 检查是否有之前的进度
    progress = resume_manager.get_script_progress(script_name)
    if progress:
        print(f"发现之前的进度记录:")
        print(f"  状态: {progress['status']}")
        print(f"  已完成: {progress['completed_items']}/{progress['total_items']}")
        print(f"  最后处理: {progress['last_processed_item']}")
        
        # 获取已完成的股票
        completed_stocks = resume_manager.get_completed_stocks(script_name)
        print(f"  已完成股票数: {len(completed_stocks)}")
        
        # 从未完成的股票开始
        remaining_stocks = [stock for stock in stock_list if stock not in completed_stocks]
        print(f"  剩余股票数: {len(remaining_stocks)}")
        
        choice = input("是否断点续传? (y/n): ").lower()
        if choice == 'y':
            stock_list = remaining_stocks
            print("选择断点续传，从剩余股票开始处理")
        else:
            # 清除进度，重新开始
            resume_manager.clear_script_progress(script_name)
            print("选择重新开始，清除之前的进度")
    
    # 开始处理
    resume_manager.save_script_progress(
        script_name, 'running',
        total_items=len(stock_list),
        completed_items=0,
        failed_items=0
    )
    
    completed = 0
    failed = 0
    
    try:
        for i, stock_code in enumerate(stock_list):
            print(f"正在处理 {stock_code} ({i+1}/{len(stock_list)})")
            
            # 模拟数据获取过程
            time.sleep(0.1)  # 模拟网络请求时间
            
            # 模拟随机失败
            import random
            if random.random() < 0.05:  # 5%的失败率
                print(f"  处理 {stock_code} 失败")
                failed += 1
            else:
                print(f"  处理 {stock_code} 成功")
                completed += 1
                
                # 保存股票进度
                resume_manager.save_stock_progress(
                    script_name, stock_code, 
                    datetime.now().strftime('%Y%m%d'),
                    'completed', record_count=100
                )
            
            # 更新总体进度
            resume_manager.save_script_progress(
                script_name, 'running',
                total_items=len(stock_list),
                completed_items=completed,
                failed_items=failed,
                last_processed_item=stock_code
            )
            
            # 模拟中断（每20只股票有10%的概率中断）
            if (i + 1) % 20 == 0 and random.random() < 0.1:
                print(f"\n模拟程序中断在第 {i+1} 只股票")
                resume_manager.save_script_progress(
                    script_name, 'interrupted',
                    total_items=len(stock_list),
                    completed_items=completed,
                    failed_items=failed,
                    last_processed_item=stock_code,
                    notes=f"程序在处理第{i+1}只股票时中断"
                )
                return
        
        # 完成所有处理
        resume_manager.save_script_progress(
            script_name, 'completed',
            total_items=len(stock_list),
            completed_items=completed,
            failed_items=failed,
            notes=f"成功完成所有处理，成功: {completed}, 失败: {failed}"
        )
        
        print(f"\n数据获取完成!")
        print(f"成功: {completed}, 失败: {failed}")
        
    except KeyboardInterrupt:
        print(f"\n用户中断程序")
        resume_manager.save_script_progress(
            script_name, 'interrupted',
            total_items=len(stock_list),
            completed_items=completed,
            failed_items=failed,
            last_processed_item=stock_list[i] if i < len(stock_list) else None,
            notes="用户手动中断程序"
        )

def show_progress():
    """显示所有脚本的进度"""
    resume_manager = ResumeManager()
    all_progress = resume_manager.get_all_progress()
    
    if not all_progress:
        print("没有找到任何进度记录")
        return
    
    print("\n=== 数据获取进度总览 ===")
    print(f"{'脚本名称':<20} {'状态':<10} {'进度':<10} {'完成/总数':<15} {'失败':<8} {'最后运行时间':<20}")
    print("-" * 90)
    
    for progress in all_progress:
        print(f"{progress['script_name']:<20} "
              f"{progress['status']:<10} "
              f"{progress['progress_percent']:>6.1f}% "
              f"{progress['completed_items']:>4}/{progress['total_items']:<4} "
              f"{progress['failed_items']:>6} "
              f"{progress['last_run_time'][:19] if progress['last_run_time'] else 'N/A':<20}")

def clear_progress():
    """清除所有进度记录"""
    resume_manager = ResumeManager()
    
    # 获取所有进度记录
    all_progress = resume_manager.get_all_progress()
    if not all_progress:
        print("没有找到任何进度记录")
        return
    
    print("找到以下进度记录:")
    for progress in all_progress:
        print(f"  - {progress['script_name']} ({progress['status']})")
    
    choice = input("确定要清除所有进度记录吗? (y/n): ").lower()
    if choice == 'y':
        for progress in all_progress:
            resume_manager.clear_script_progress(progress['script_name'])
        print("已清除所有进度记录")
    else:
        print("取消清除操作")

def test_script_wrapper():
    """测试脚本包装器"""
    # 测试一个实际的脚本
    script_path = "market_data/daily.py"
    if not os.path.exists(script_path):
        print(f"脚本文件不存在: {script_path}")
        return
    
    wrapper = ScriptWrapper(script_path)
    
    # 检查断点续传可能性
    can_resume, message = wrapper.check_resume_possibility()
    print(f"脚本: {script_path}")
    print(f"可以断点续传: {can_resume}")
    print(f"消息: {message}")
    
    if can_resume:
        resume_info = wrapper.get_resume_info()
        print("\n断点续传信息:")
        for key, value in resume_info.items():
            print(f"  {key}: {value}")

def main():
    """主函数"""
    print("=== 断点续传功能测试 ===")
    print("1. 模拟数据获取（支持断点续传）")
    print("2. 查看进度")
    print("3. 清除进度记录")
    print("4. 测试脚本包装器")
    print("5. 退出")
    
    while True:
        choice = input("\n请选择操作 (1-5): ").strip()
        
        if choice == '1':
            simulate_data_fetch()
        elif choice == '2':
            show_progress()
        elif choice == '3':
            clear_progress()
        elif choice == '4':
            test_script_wrapper()
        elif choice == '5':
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
