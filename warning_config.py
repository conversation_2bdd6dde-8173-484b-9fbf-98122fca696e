#!/usr/bin/env python3
"""
全局警告配置模块
在所有数据获取脚本中导入此模块以统一配置警告过滤
"""

import warnings

def configure_warnings():
    """配置警告过滤器"""
    
    # 过滤tushare相关的FutureWarning
    warnings.filterwarnings(
        'ignore',
        category=FutureWarning,
        message=".*fillna with 'method' is deprecated.*"
    )
    
    # 过滤所有来自tushare模块的FutureWarning
    warnings.filterwarnings(
        'ignore',
        category=FutureWarning,
        module='tushare.*'
    )
    
    # 过滤pandas相关的FutureWarning（如果需要）
    warnings.filterwarnings(
        'ignore',
        category=FutureWarning,
        message=".*Series.fillna with 'method' is deprecated.*"
    )
    
    # 可以添加更多特定的警告过滤
    # warnings.filterwarnings('ignore', category=DeprecationWarning)
    
    print("✅ 警告过滤器已配置")

# 自动执行配置
configure_warnings()
