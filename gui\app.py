#!/usr/bin/env python3
"""
股票数据获取GUI管理面板
主启动文件
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

try:
    from gui.main_panel import StockDataGUIPanel
except ImportError as e:
    print(f"导入GUI模块失败: {e}")
    print("请确保所有依赖模块都已正确安装")
    sys.exit(1)

def check_dependencies():
    """检查必要的依赖"""
    missing_deps = []
    
    # 检查必要的Python包
    required_packages = [
        'tkinter',
        'sqlite3',
        'concurrent.futures',
        'threading',
        'queue'
    ]
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_deps.append(package)
    
    # 检查项目特定的模块
    try:
        import tushare
    except ImportError:
        missing_deps.append('tushare')
    
    try:
        import pandas
    except ImportError:
        missing_deps.append('pandas')
    
    if missing_deps:
        error_msg = f"缺少以下依赖包: {', '.join(missing_deps)}\n"
        error_msg += "请使用以下命令安装:\n"
        error_msg += f"pip install {' '.join(missing_deps)}"
        print(error_msg)
        return False, error_msg
    
    return True, "依赖检查通过"

def check_project_structure():
    """检查项目结构"""
    required_dirs = [
        'basic_data',
        'market_data', 
        'financial_data',
        'moneyflow_data',
        'reference_data',
        'special_data',
        'dapan_topic_data',
        'config'
    ]
    
    missing_dirs = []
    for dir_name in required_dirs:
        dir_path = os.path.join(root_dir, dir_name)
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_name)
    
    if missing_dirs:
        warning_msg = f"以下目录不存在，可能影响功能: {', '.join(missing_dirs)}"
        print(f"警告: {warning_msg}")
        return False, warning_msg
    
    return True, "项目结构检查通过"

def main():
    """主函数"""
    print("正在启动股票数据获取GUI管理面板...")
    
    # 检查依赖
    deps_ok, deps_msg = check_dependencies()
    if not deps_ok:
        print(f"依赖检查失败: {deps_msg}")
        return
    
    # 检查项目结构
    struct_ok, struct_msg = check_project_structure()
    if not struct_ok:
        print(f"项目结构检查: {struct_msg}")
    
    try:
        # 创建主窗口
        root = tk.Tk()
        
        # 设置窗口图标（如果有的话）
        try:
            # 可以在这里设置窗口图标
            # root.iconbitmap('icon.ico')
            pass
        except:
            pass
        
        # 创建应用实例
        app = StockDataGUIPanel(root)
        
        # 显示启动信息
        app.log_message("股票数据获取GUI管理面板已启动", "SUCCESS")
        app.log_message(f"项目根目录: {root_dir}", "INFO")
        
        # 自动检查数据库连接
        app.check_database_connection()
        
        # 自动刷新脚本列表
        app.refresh_script_list()
        
        print("GUI界面已启动，请在窗口中进行操作")
        
        # 启动主循环
        root.mainloop()
        
    except Exception as e:
        error_msg = f"启动GUI时发生错误: {str(e)}"
        print(error_msg)
        
        # 尝试显示错误对话框
        try:
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            messagebox.showerror("启动错误", error_msg)
        except:
            pass
        
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
