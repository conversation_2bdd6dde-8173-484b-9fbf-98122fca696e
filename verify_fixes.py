#!/usr/bin/env python3
"""
验证数据库脚本修复效果
"""

import os
import re

def check_script_fix_status(script_path):
    """检查脚本修复状态"""
    if not os.path.exists(script_path):
        return "❌", "文件不存在"
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有必要的修复元素
        has_check_method = "def check_data_exists" in content
        has_skip_logic = "数据已存在，跳过更新" in content
        has_ts_code_param = "def insert_data(self, df, ts_code=None)" in content
        has_error_handling = "UNIQUE constraint failed" in content
        
        if has_check_method and has_skip_logic and has_ts_code_param and has_error_handling:
            return "✅", "已完全修复"
        elif has_check_method or has_skip_logic:
            return "🔄", "部分修复"
        else:
            return "❌", "未修复"
            
    except Exception as e:
        return "❓", f"检查失败: {e}"

def main():
    """主函数"""
    print("=" * 60)
    print("数据库脚本修复状态验证")
    print("=" * 60)
    
    scripts_to_check = [
        ("market_data/adj_factor.py", "复权因子"),
        ("market_data/daily_basic.py", "每日指标"),
        ("market_data/weekly.py", "周线数据"),
        ("market_data/monthly.py", "月线数据"),
        ("market_data/pro_bar.py", "复权行情"),
        ("market_data/realtime_quote.py", "实时行情"),
        ("market_data/realtime_tick.py", "实时成交"),
        ("market_data/stk_mins.py", "分钟线数据")
    ]
    
    print("修复状态检查:")
    print("-" * 40)
    
    fixed_count = 0
    total_count = len(scripts_to_check)
    
    for script_path, description in scripts_to_check:
        status_icon, status_msg = check_script_fix_status(script_path)
        print(f"{status_icon} {script_path}")
        print(f"   {description}: {status_msg}")
        
        if status_icon == "✅":
            fixed_count += 1
    
    print(f"\n修复进度: {fixed_count}/{total_count} ({fixed_count/total_count*100:.1f}%)")
    
    print("\n" + "=" * 60)
    print("修复效果说明")
    print("=" * 60)
    
    print("✅ 已修复的脚本特征:")
    print("  • 有 check_data_exists 方法")
    print("  • 有数据存在性检查逻辑")
    print("  • insert_data 方法支持 ts_code 参数")
    print("  • 优雅处理 UNIQUE constraint 错误")
    
    print("\n🔄 部分修复:")
    print("  • 有部分修复元素，但可能不完整")
    
    print("\n❌ 未修复:")
    print("  • 仍使用简单的 append 模式")
    print("  • 可能遇到 UNIQUE constraint failed 错误")
    
    print("\n💡 修复后的预期行为:")
    print("  • 遇到重复数据时显示: '数据已存在，跳过更新'")
    print("  • 避免程序因重复数据而中断")
    print("  • 提高数据获取效率")

if __name__ == "__main__":
    main()
