import os
import sys
import time
import pandas as pd
import sqlite3
import tushare as ts
from collections import deque
from datetime import datetime, timedelta
from threading import Lock

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

from config import get_token

# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

# 频率控制相关变量
REQUEST_LIMIT = 300  # 每分钟请求限制（5000积分）
WINDOW_SIZE = 60    # 时间窗口大小（秒）
request_times = deque()  # 存储请求时间
request_lock = Lock()   # 线程锁，用于同步访问

class StockDatabase:
    def __init__(self, db_path=None):
        """初始化数据库连接"""
        if db_path is None:
            # 使用项目根目录下的data.db
            db_path = os.path.join(root_dir, 'data.db')
        self.db_name = db_path
        self.conn = sqlite3.connect(db_path)
        print(f"数据库 {db_path} 连接成功")

    def create_table(self):
        """创建中央结算系统持股汇总表"""
        try:
            create_table_sql = '''CREATE TABLE IF NOT EXISTS ccass_hold (
                trade_date TEXT,
                ts_code TEXT,
                name TEXT,
                shareholding TEXT,
                hold_nums TEXT,
                hold_ratio TEXT,
                PRIMARY KEY (ts_code, trade_date)
            )'''
            
            # 首先删除已存在的表
            self.conn.execute('DROP TABLE IF EXISTS ccass_hold')
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 ccass_hold 创建成功")
            
        except Exception as e:
            print(f"创建表格 ccass_hold 时出错: {e}")
            raise

    def insert_data(self, df):
        """将DataFrame数据插入到表格中"""
        try:
            # 创建临时表
            temp_table_name = 'temp_ccass_hold'
            df.to_sql(temp_table_name, self.conn, if_exists='replace', index=False)
            
            # 使用INSERT OR REPLACE将数据从临时表插入到主表
            insert_sql = f'''
                INSERT OR REPLACE INTO ccass_hold 
                SELECT * FROM {temp_table_name}
            '''
            self.conn.execute(insert_sql)
            
            # 删除临时表
            self.conn.execute(f'DROP TABLE IF EXISTS {temp_table_name}')
            
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")
        except Exception as e:
            print(f"写入数据时出错: {e}")
            self.conn.rollback()

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_stock_list():
    """获取港股列表"""
    try:
        # 使用hk_basic接口获取港股列表
        df = pro.hk_basic(list_status='L')
        if df is None or df.empty:
            print("未获取到港股列表数据")
            return []
            
        # 确保ts_code列存在且不为空
        if 'ts_code' not in df.columns:
            print("获取的数据中没有ts_code列")
            return []
            
        # 过滤掉任何空值
        df = df.dropna(subset=['ts_code'])
        
        # 确保所有的ts_code都是字符串格式
        stock_list = df['ts_code'].astype(str).tolist()
        
        print(f"成功获取到 {len(stock_list)} 只港股")
        return stock_list
    except Exception as e:
        print(f"获取股票列表时出错: {e}")
        return []

def get_date_ranges():
    """获取近5年的日期范围，按季度划分"""
    ranges = []
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3*365)  # 3年前
    
    # 将时间范围按季度划分
    current_date = end_date
    while current_date > start_date:
        quarter_end = current_date.strftime('%Y%m%d')
        current_date = current_date - timedelta(days=90)  # 大约一个季度
        quarter_start = current_date.strftime('%Y%m%d')
        ranges.append((quarter_start, quarter_end))
    
    return ranges

def control_request_rate():
    """控制请求频率，确保每分钟不超过300次请求"""
    with request_lock:
        current_time = time.time()
        
        # 移除一分钟之前的请求记录
        while request_times and current_time - request_times[0] >= WINDOW_SIZE:
            request_times.popleft()
        
        # 如果在当前时间窗口内的请求数达到限制
        if len(request_times) >= REQUEST_LIMIT:
            # 计算需要等待的时间
            wait_time = WINDOW_SIZE - (current_time - request_times[0])
            if wait_time > 0:
                time.sleep(wait_time)
                current_time = time.time()  # 更新当前时间
        
        # 记录新的请求时间
        request_times.append(current_time)

def fetch_ccass_data(ts_code, start_date, end_date, max_retries=3):
    """获取指定股票和日期范围的中央结算系统持股数据"""
    for retry in range(max_retries):
        try:
            print(f"正在获取 {ts_code} 从 {start_date} 至 {end_date} 的中央结算系统持股数据...")
            
            # 控制请求频率
            control_request_rate()
            
            # 发起请求
            df = pro.ccass_hold(ts_code=ts_code, start_date=start_date, end_date=end_date)
            
            if df is not None and not df.empty:
                return df
            elif retry < max_retries - 1:
                print(f"获取到空数据，将在3秒后重试（第{retry + 1}次）...")
                time.sleep(3)
            else:
                print(f"连续{max_retries}次获取到空数据，跳过处理")
                return None
                
        except Exception as e:
            if retry < max_retries - 1:
                print(f"获取数据出错: {e}，将在3秒后重试（第{retry + 1}次）...")
                time.sleep(3)
            else:
                print(f"获取 {ts_code} 的中央结算系统持股数据时出错: {e}")
                return None
    
    return None

def process_stock_data(ts_code, date_ranges, db):
    """处理单个股票的所有时期数据"""
    for start_date, end_date in date_ranges:
        df = fetch_ccass_data(ts_code, start_date, end_date)
        if df is not None and not df.empty:
            db.insert_data(df)
            print(f"成功获取 {ts_code} 从 {start_date} 至 {end_date} 的数据，共 {len(df)} 条记录")
        else:
            print(f"跳过 {ts_code} 从 {start_date} 至 {end_date} 的处理")

def main():
    # 确保数据目录存在
    os.makedirs(os.path.dirname(os.path.join(root_dir, 'data.db')), exist_ok=True)
    
    # 创建数据库实例
    db = StockDatabase()
    
    try:
        # 创建表
        db.create_table()
        
        # 获取股票列表
        stock_list = get_stock_list()
        if not stock_list:
            print("获取股票列表失败")
            return
        
        # 获取日期范围列表
        date_ranges = get_date_ranges()
        print(f"将获取以下日期范围的数据: {date_ranges}")
        
        # 处理每只股票
        total_stocks = len(stock_list)
        for idx, ts_code in enumerate(stock_list, 1):
            print(f"正在处理第 {idx}/{total_stocks} 只股票: {ts_code}")
            process_stock_data(ts_code, date_ranges, db)
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行中央结算系统持股汇总数据获取程序...")
    main()
    print("程序运行完成！") 