<!DOCTYPE html>
<html lang="zh-CN">

<head>
    
<title>腾讯网</title>
<meta name="keywords" content="资讯,新闻,财经,房产,视频,NBA,科技,腾讯网,腾讯,QQ,Tencent">
<meta name="description" content="腾讯网从2003年创立至今，已经成为集新闻信息，区域垂直生活服务、社会化媒体资讯和产品为一体的互联网媒体平台。腾讯网下设新闻、科技、财经、娱乐、体育、汽车、时尚等多个频道，充分满足用户对不同类型资讯的需求。同时专注不同领域内容，打造精品栏目，并顺应技术发展趋势，推出网络直播等创新形式，改变了用户获取资讯的方式和习惯。">
<meta name="author" content="腾讯网">
<meta name="copyright" content="Copyright 1998 - 2025 Tencent. All Rights Reserved">
<meta property="og:type" content="news" />


<meta name="baidu-site-verification" content="code-mzfngMK684" />
    <meta charset="utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
<link rel="dns-prefetch" href="mat1.gtimg.com">
<link rel="dns-prefetch" href="i.news.qq.com">
<link rel="shortcut icon" href="https://mat1.gtimg.com/qqcdn/qqindex2021/favicon.ico">
<script nomodule="true" src="https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/20240515201444/core3-37-1.min.js"></script>
<script>
  try {
    if (!window.IntersectionObserver) {
      var observerScript = document.createElement('script');
      observerScript.src = "https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/20241024141058/intersection-observer-polyfill.js";
      document.head.appendChild(observerScript);
    }
  } catch (error) {}
</script>

<script>
  try {
    if (!Element.prototype.scrollTo) {
      var scrollScript = document.createElement('script');
      scrollScript.src = "https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/20241025153001/scroll-behavior-polyfill.js";
      document.head.appendChild(scrollScript);
    }
  } catch (error) {}
</script>
<script>
  try {
    if ('scrollRestoration' in window.history) {
      window.history.scrollRestoration = 'manual';
    }
    window.isPcClient = Boolean(window.electron) && (
      window.navigator.userAgent.indexOf('pc-client') > 0 ||
      window.navigator.userAgent.indexOf('TencentNews') > 0
    );
  } catch {}
</script>
<script>
  window.channelInfo = {"IS_EDU":false,"IS_SCHEDULE":false,"IS_MEDAL_RANKING":false,"IS_OLYMPIC_MATCH_REPLAY":false,"IS_OLYMPIC_MATCH_HIGHLIGHT":false,"IS_QA_CHANNEL":false,"IS_VIDEO_CHANNEL":false,"IS_GAME_CENTER":false,"IS_CHANNEL2":false,"IS_LOCAL_SITE":false,"IS_AUTHOR_PAGE":false,"IS_CHANNEL":true,"IS_DARK":false,"channelKey":"finance","channelType":"channel","channelConfig":{"channelRight":[{"_auto_id":"3","channel_name_en":"finance","modules":"{\"moduleList\":[{\"title\":\"精选视频\",\"id\":\"video_album\",\"videoType\":\"cid\",\"videoId\":\"\",\"isSticky\":0},{\"id\":\"daily_paper\",\"morningStartTime\":\"06:30:00\",\"morningEndTime\":\"17:59:59\",\"morningConfig\":{\"tagId\":\"bEemwrpehGc=\",\"name\":\"财经早报\",\"coverImg\":\"https://inews.gtimg.com/newsapp_bt/0/1126130603283_4561/0\"},\"eveningConfig\":{\"tagId\":\"bEerx71ajmI=\",\"name\":\"财经晚报\",\"coverImg\":\"https://inews.gtimg.com/newsapp_bt/0/1126130547624_9074/0\"}},{\"title\":\"信息流\",\"id\":\"ad_tuiguang\",\"adType\":\"info\",\"category\":\"ad_block\",\"path\":\"financeChannelTuiGuang\",\"isSticky\":0},{\"title\":\"股市指数看板\",\"id\":\"stock\",\"isSticky\":0},{\"title\":\"下载条\",\"id\":\"download_banner\",\"isSticky\":1},{\"title\":\"热点榜\",\"id\":\"hot_rank_list\",\"isSticky\":1},{\"title\":\"广告推广位\",\"id\":\"ad_tuiguang\",\"category\":\"l_qq_com\",\"path\":\"QQcom_all_Rectangle-1\",\"isSticky\":1}]}"}],"channelTop":[],"channelNav":[{"_auto_id":"1","active_alien_img":"","alien_img":"","channel_id":"news_news_home","is_local":"0","link":"https://www.qq.com","name_cn":"首页","name_en":"home"},{"_auto_id":"2","active_alien_img":"","alien_img":"","channel_id":"news_news_top","is_local":"0","link":"","name_cn":"要闻","name_en":"news"},{"_auto_id":"4","active_alien_img":"","alien_img":"","channel_id":"news_news_bj","is_local":"1","link":"","name_cn":"北京","name_en":"bj"},{"_auto_id":"5","active_alien_img":"","alien_img":"","channel_id":"news_news_finance","is_local":"0","link":"","name_cn":"财经","name_en":"finance"},{"_auto_id":"6","active_alien_img":"","alien_img":"","channel_id":"news_news_tech","is_local":"0","link":"","name_cn":"科技","name_en":"tech"},{"_auto_id":"7","active_alien_img":"","alien_img":"","channel_id":"tv","is_local":"0","link":"https://v.qq.com/channel/tv/?ptag=qqnews","name_cn":"电视剧","name_en":"tv"},{"_auto_id":"8","active_alien_img":"","alien_img":"","channel_id":"news_news_qa","is_local":"0","link":"","name_cn":"热问","name_en":"qa"},{"_auto_id":"9","active_alien_img":"","alien_img":"","channel_id":"news_news_ent","is_local":"0","link":"","name_cn":"娱乐","name_en":"ent"},{"_auto_id":"10","active_alien_img":"","alien_img":"","channel_id":"variety","is_local":"0","link":"https://v.qq.com/channel/variety/?ptag=qqnews","name_cn":"综艺","name_en":"variety"},{"_auto_id":"11","active_alien_img":"","alien_img":"","channel_id":"news_news_sports","is_local":"0","link":"","name_cn":"体育","name_en":"sports"},{"_auto_id":"13","active_alien_img":"","alien_img":"","channel_id":"news_news_nba","is_local":"0","link":"","name_cn":"NBA","name_en":"nba"},{"_auto_id":"14","active_alien_img":"","alien_img":"","channel_id":"news_news_world","is_local":"0","link":"","name_cn":"国际","name_en":"world"},{"_auto_id":"15","active_alien_img":"","alien_img":"","channel_id":"news_news_mil","is_local":"0","link":"","name_cn":"军事","name_en":"milite"},{"_auto_id":"16","active_alien_img":"","alien_img":"","channel_id":"news_news_auto","is_local":"0","link":"","name_cn":"汽车","name_en":"auto"},{"_auto_id":"17","active_alien_img":"","alien_img":"","channel_id":"news_news_house","is_local":"0","link":"","name_cn":"房产","name_en":"house"},{"_auto_id":"18","active_alien_img":"","alien_img":"","channel_id":"news_news_edu","is_local":"0","link":"","name_cn":"教育","name_en":"edu"},{"_auto_id":"19","active_alien_img":"","alien_img":"","channel_id":"news_news_antip","is_local":"0","link":"","name_cn":"健康","name_en":"health"},{"_auto_id":"20","active_alien_img":"","alien_img":"","channel_id":"news_news_video","is_local":"0","link":"","name_cn":"视频","name_en":"video"},{"_auto_id":"21","active_alien_img":"","alien_img":"","channel_id":"news_news_game","is_local":"0","link":"","name_cn":"游戏","name_en":"games"},{"_auto_id":"22","active_alien_img":"","alien_img":"","channel_id":"news_news_nchupin","is_local":"0","link":"","name_cn":"眼界","name_en":"chupin"},{"_auto_id":"24","active_alien_img":"","alien_img":"","channel_id":"news_news_football","is_local":"0","link":"","name_cn":"足球","name_en":"football"},{"_auto_id":"25","active_alien_img":"","alien_img":"","channel_id":"news_news_kepu","is_local":"0","link":"","name_cn":"科学","name_en":"kepu"},{"_auto_id":"26","active_alien_img":"","alien_img":"","channel_id":"news_news_digi","is_local":"0","link":"","name_cn":"数码","name_en":"digi"},{"_auto_id":"28","active_alien_img":"","alien_img":"","channel_id":"ymzx","is_local":"0","link":"https://gamer.qq.com/v2/cloudgame/game/96897?ichannel=txxwpc0Ftxxwpc1","name_cn":"元梦之星","name_en":"news_news_ymzx"},{"_auto_id":"31","active_alien_img":"","alien_img":"","channel_id":"movie","is_local":"0","link":"https://v.qq.com/channel/movie/?ptag=qqnews","name_cn":"电影","name_en":"movie"},{"_auto_id":"32","active_alien_img":"","alien_img":"","channel_id":"news_news_esport","is_local":"0","link":"","name_cn":"电竞","name_en":"esport"},{"_auto_id":"34","active_alien_img":"","alien_img":"","channel_id":"news_news_history","is_local":"0","link":"","name_cn":"历史","name_en":"history"},{"_auto_id":"35","active_alien_img":"","alien_img":"","channel_id":"news_news_baby","is_local":"0","link":"","name_cn":"育儿","name_en":"baby"},{"_auto_id":"36","active_alien_img":"","alien_img":"","channel_id":"hbjy","is_local":"0","link":"https://gp.qq.com/act/a20250421mnqlx/news.shtml","name_cn":"和平精英","name_en":"news_news_hbjy"},{"_auto_id":"37","active_alien_img":"","alien_img":"","channel_id":"cloud_gamer","is_local":"0","link":"https://gamer.qq.com/?ichannel=txxwpc0Ftxxwpc1","name_cn":"云游戏","name_en":"cloud_gamer"},{"_auto_id":"38","active_alien_img":"","alien_img":"","channel_id":"news_news_lic","is_local":"0","link":"","name_cn":"理财","name_en":"finance_licai"},{"_auto_id":"39","active_alien_img":"","alien_img":"","channel_id":"news_news_istock","is_local":"0","link":"","name_cn":"股票","name_en":"finance_stock"},{"_auto_id":"40","active_alien_img":"","alien_img":"","channel_id":"ren_min_shi_pin","is_local":"0","link":"https://news.qq.com/omn/author/8QMd3Hld74cbujbY?tab=om_video","name_cn":"人民视频","name_en":"ren_min_shi_pin"},{"_auto_id":"41","active_alien_img":"","alien_img":"","channel_id":"news_news_weather","is_local":"0","link":"https://tianqi.qq.com/index.htm","name_cn":"天气","name_en":"weather"}],"channelNav2":[{"_auto_id":"1","channel1_name_en":"edu","channel_id":"news_news_college","link":"","name_cn":"高校","name_en":"college"},{"_auto_id":"3","channel1_name_en":"edu","channel_id":"news_news_gaokao","link":"","name_cn":"高考","name_en":"gaokao"},{"_auto_id":"7","channel1_name_en":"edu","channel_id":"news_news_abroad","link":"","name_cn":"出国","name_en":"abroad"},{"_auto_id":"8","channel1_name_en":"edu","channel_id":"news_news_zxx","link":"","name_cn":"素质教育","name_en":"zxx"},{"_auto_id":"9","channel1_name_en":"edu","channel_id":"news_news_ky","link":"","name_cn":"考研","name_en":"ky"},{"_auto_id":"10","channel1_name_en":"edu","channel_id":"news_news_mba","link":"","name_cn":"商学院","name_en":"mba"},{"_auto_id":"11","channel1_name_en":"edu","channel_id":"news_news_career","link":"","name_cn":"职业教育","name_en":"career"}],"channelSkin":[],"channelAd":[{"_auto_id":"22","channel_name_en":"finance","modules":"{\"moduleList\":[{\"title\":\"广告推广位\",\"id\":\"top\",\"category\":\"l_qq_com\",\"path\":\"QQcom_all_Width1\"}]}"}],"channelSwiperAd":[],"channelGameRight":[{"_auto_id":"2","desc":"连续登录送游戏钻石，群雄共聚称霸沙城","icon":"https://inews.gtimg.com/newsapp_bt/0/0627161037914_3816/0","link":"https://s.iwan.qq.com/opengame/tenvideo/index.html?hidestatusbar=1&hidetitlebar=1&immersive=1&syswebview=1&landscape=1&gameid=49085&url=https%3A%2F%2Fgz-file.91ninthpalace.com%2Fwzzx%2Findex_tencent_iwan.html%20&ref_ele=90015","name":"王者之心2"},{"_auto_id":"3","desc":"上线送VIP！万人同屏横扫沙城","icon":"https://inews.gtimg.com/newsapp_bt/0/0627155752146_4584/0","link":"https://s.iwan.qq.com/opengame/tenvideo/index.html?hidestatusbar=1&hidetitlebar=1&immersive=1&landscape=1&syswebview=1&gameid=47203&url=https%3A%2F%2Fcqss2login.bigrnet.com%2Fiwan%2Fh5%2Fplay%2Floading&ref_ele=90015","name":"传奇盛世"},{"_auto_id":"4","desc":"超高爆率，经典玩法","icon":"https://inews.gtimg.com/newsapp_bt/0/0627160641137_9103/0","link":"https://s.iwan.qq.com/opengame/tenvideo/index.html?hidestatusbar=1&hidetitlebar=1&immersive=1&syswebview=1&gameid=43803&url=https%3A%2F%2Fsdk.mxzgame.com%2FGames%2Fportal%2F108337%2FTXVApp&ref_ele=90015","name":"新不良人"},{"_auto_id":"6","desc":"超多福利登录即领，海量游戏任你畅玩","icon":"https://inews.gtimg.com/newsapp_bt/0/111315495935_3595/0","link":"https://dldir3.qq.com/minigamefile/webdownloads/QQGameMini_silent_1002020001_cid0.exe","name":"QQ游戏大厅"},{"_auto_id":"7","desc":"纯正经典玩法，欢乐挑战赛火热来袭","icon":"https://inews.gtimg.com/newsapp_bt/0/070918050891_4971/0","link":"https://minigame.qq.com/h5game_frame_test/?appid=200904&ifid=1502020001","name":"欢乐斗地主"},{"_auto_id":"8","desc":"新服大放送，享赚你就来","icon":"https://inews.gtimg.com/newsapp_bt/0/0627154608860_7318/0","link":"https://s.iwan.qq.com/opengame/tenvideo/index.html?hidestatusbar=1&hidetitlebar=1&immersive=1&syswebview=1&landscape=1&gameid=43403&url=https%3A%2F%2Flogin-wxxyx2-bzsc.jikewan.com%2Fgame%2Fcqtxvideo.html&ref_ele=90015","name":"百战沙城"},{"_auto_id":"9","desc":"全新极速版本爽玩！送新武魂转换卡","icon":"https://inews.gtimg.com/newsapp_bt/0/1016115936984_7153/0","link":"https://s.iwan.qq.com/opengame/tenvideo/index.html?hidestatusbar=1&hidetitlebar=1&immersive=1&syswebview=1&gameid=51477&url=https%3A%2F%2Fh5sdk.cdqcwl.com%2Fsdk%2Ftxaiwandefault%2Fce43a6806214ed5b3e2227ca7e99e27a%2F2231&ref_ele=90015","name":"斗罗大陆"},{"_auto_id":"10","desc":"原汁原味，正版授权","icon":"https://inews.gtimg.com/newsapp_bt/0/0627160844946_1794/0","link":"https://s.iwan.qq.com/opengame/tenvideo/index.html?hidetitlebar=1&immersive=1&syswebview=1&landscape=1&gameid=37275&url=https%3A%2F%2Fsdk.mxzgame.com%2FGames%2Fportal%2F100211%2FTXVApp&ref_ele=90015","name":"原始传奇"},{"_auto_id":"11","desc":"登录领神秘巨星，打造巅峰阵容","icon":"https://inews.gtimg.com/newsapp_bt/0/0701170959368_8122/0","link":"https://s.iwan.qq.com/opengame/tenvideo/index.html?hidestatusbar=1&hidetitlebar=1&immersive=1&syswebview=1&gameid=40591&url=https%3A%2F%2Frh.diaigame.com%2Fh5plat%2Fplay%2Fpackage_code%2FP0012462&ref_ele=90015","name":"巅峰冠军足球"},{"_auto_id":"12","desc":"赛季制实时PVP联机对战","icon":"https://inews.gtimg.com/newsapp_bt/0/0701165259701_7142/0","link":"https://s.iwan.qq.com/opengame/tenvideo/index.html?hidestatusbar=1&hidetitlebar=1&immersive=1&syswebview=1&gameid=49634&url=https%3A%2F%2Ffootball.shenshoucdn.com%2Ffootball_new%2Fh5%2Ftxsp%2Findex.html&ref_ele=90015","name":"球场风云"},{"_auto_id":"13","desc":"专注超爽打宝体验","icon":"https://inews.gtimg.com/newsapp_bt/0/0627154956673_3154/0","link":"https://s.iwan.qq.com/opengame/tenvideo/index.html?hidestatusbar=1&hidetitlebar=1&immersive=1&syswebview=1&gameid=41057&url=https%3A%2F%2Fh5apily.fire2333.com%2Fh5sdk%2Ftxshipin%2Findex%2F3200222%2F3200112&ref_ele=90015","name":"传奇至尊"},{"_auto_id":"16","desc":"火爆新服，福利满满","icon":"https://inews.gtimg.com/newsapp_bt/0/0701171307639_4759/0","link":"https://s.iwan.qq.com/opengame/tenvideo/index.html?hidestatusbar=1&hidetitlebar=1&immersive=1&syswebview=1&gameid=50335&url=https%3A%2F%2Fh5-union-cdn.pptgame.cn%2Findex.html%3Ftx_package_id%3D10202%20&ref_ele=90015","name":"火源战纪"},{"_auto_id":"17","desc":"魔幻风格，超大场面","icon":"https://inews.gtimg.com/newsapp_bt/0/0701171500721_6895/0","link":"https://s.iwan.qq.com/opengame/tenvideo/index.html?hidestatusbar=1&hidetitlebar=1&immersive=1&syswebview=1&gameid=33112&url=https%3A%2F%2Fcsjs-tx.ebibi.com%2Fgame%2Fh5iwan-wwzs%2Fmain%2Findex.html&ref_ele=90015","name":"万王之神"},{"_auto_id":"19","desc":"经典神话背景，高清细腻画质","icon":"https://inews.gtimg.com/newsapp_bt/0/0709181543493_4955/0","link":"https://s.iwan.qq.com/opengame/tenvideo/index.html?hidestatusbar=1&hidetitlebar=1&immersive=1&syswebview=1&gameid=39686&url=https%3A%2F%2Fsdk.gz.1253361160.clb.myqcloud.com%2FGames%2Fportal%2F108311%2FTXVApp&ref_ele=90015","name":"凡人神将传"}]}};
</script>
<script src="https://mat1.gtimg.com/www/js/emonitor/custom_ed041a23.js" charset="utf-8"></script>
<script>
  try {
    window.emonitorIns = emonitor.create({
      name: 'newsqq_channel',
      atta: {
        name: 'newsqq',
      },
      mode: '007',
    });
  } catch (err) {
    console.warn(err);
  }
</script>
<link href="https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/hel/qqnews-pc-channel_20250408050453/static/css/index.css" rel="stylesheet">

<script>window.__HEL_PRESET_META__={"qqnews-pc-components":{"app":{"id":1366,"name":"qqnews-pc-components","app_group_name":"qqnews-pc-components","proj_ver":{"map":{},"utime":0},"online_version":"qqnews-pc-components_20250306025658","build_version":"qqnews-pc-components_20250512030958","update_at":"2025-05-12T07:10:51.000Z","desc":"set by [init], from container [formal.pc.dc.tj100986] worker [1]"},"version":{"sub_app_name":"qqnews-pc-components","sub_app_version":"qqnews-pc-components_20250512030958","src_map":{"webDirPath":"https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/hel/qqnews-pc-components_20250512030958","htmlIndexSrc":"https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/hel/qqnews-pc-components_20250512030958/index.html","extractMode":"all","iframeSrc":"","chunkCssSrcList":["https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/hel/qqnews-pc-components_20250512030958/static/css/index.css"],"chunkJsSrcList":["https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/hel/qqnews-pc-components_20250512030958/static/js/index.js"],"staticCssSrcList":[],"staticJsSrcList":["https://mat1.gtimg.com/qqcdn/qqindex2021/static/20231212123233/react.production.min.js","https://mat1.gtimg.com/qqcdn/qqindex2021/static/20231212123233/react-dom.production.min.js","https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/hel/hel-base-v16.js"],"relativeCssSrcList":[],"relativeJsSrcList":[],"privCssSrcList":[],"srvModSrcList":[],"headAssetList":[{"tag":"staticScript","append":false,"attrs":{"src":"https://mat1.gtimg.com/qqcdn/qqindex2021/static/20231212123233/react.production.min.js"}},{"tag":"staticScript","append":false,"attrs":{"src":"https://mat1.gtimg.com/qqcdn/qqindex2021/static/20231212123233/react-dom.production.min.js"}},{"tag":"staticScript","append":false,"attrs":{"src":"https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/hel/hel-base-v16.js"}},{"tag":"script","append":true,"attrs":{"src":"https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/hel/qqnews-pc-components_20250512030958/static/js/index.js","defer":""}},{"tag":"link","append":true,"attrs":{"href":"https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/hel/qqnews-pc-components_20250512030958/static/css/index.css","rel":"stylesheet"}}],"bodyAssetList":[]},"update_at":"2025-05-12T07:10:50.000Z","create_at":"2025-05-12T07:10:50.000Z","_worker_id":"1","_is_backup":true}}}</script>
<script>window.__VIEW_PATH__="channel.ejs";</script>
</head>

<body class="">
  <div id="root" class="channel-root channel"></div>
    <script type="text/javascript">
  var TIME_BEFORE_LOAD_CRYSTAL = Date.now();
</script>
<script type="text/javascript" src="https://mat1.gtimg.com/qqcdn/qqindex2021/advertisement/qqchannel/crystal.************.min.js" charset="utf-8"></script>
<script type="text/javascript">
  if (typeof crystal === 'undefined' && Math.random() <= 1) {
    (function() {
      var TIME_AFTER_LOAD_CRYSTAL = Date.now();
      var img = new Image(1, 1);
      img.src = "//dp3.qq.com/qqcom/?adb=1&dm=new&err=1002&blockjs=" + (TIME_AFTER_LOAD_CRYSTAL - TIME_BEFORE_LOAD_CRYSTAL);
    })();
  }
</script>
    <iframe style="display: none;" src="https://i.news.qq.com/web_backend/getWebPacUid"></iframe>
<script src="https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/20240805160928/react.production.min.js"></script>
<script src="https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/20240805160928/react-dom.production.min.js"></script>
<script src="https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/20241018171503/universal-report.min.js"></script>
<script defer type="text/javascript" src="https://mat1.gtimg.com/qqcdn/qqindex2021/libs/barrier/aria.js?appid=9327b8b06379d9d1728bbfbe2025ef9c" charset="utf-8"></script>
<script defer src="https://t.captcha.qq.com/TCaptcha.js"></script>
<script>document.cookie="hel_err=;path=/;";</script>
<script src="https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/hel/hel-base-v16.js"></script>
<script src="https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/hel/qqnews-pc-hel-entry_20250117174052/static/js/index.js"></script>
<link rel="preload" href="https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/hel/qqnews-pc-channel_20250408050453/static/js/index.js" as="script">
<link rel="preload" href="https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/hel/qqnews-pc-components_20250512030958/static/js/index.js" as="script">
<script>window.loadProject("https://mat1.gtimg.com/qqcdn/qqindex2021/common-static/hel/qqnews-pc-channel_20250408050453/static/js/index.js");</script>
<iframe id="videoFrame" style="display: none;" src="https://video.qq.com/cookie/sync_qqnews.html"></iframe>
</body>

</html>
