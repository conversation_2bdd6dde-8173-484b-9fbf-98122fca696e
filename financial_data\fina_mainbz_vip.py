import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)



# 添加项目根目录到Python路径

from config import get_token
import tushare as ts
import pandas as pd
import sqlite3
import time
from datetime import datetime, timedelta
# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

class StockDatabase:
    def __init__(self, db_name='data.db'):
        """初始化数据库连接"""
        self.db_name = db_name
        self.conn = sqlite3.connect(db_name)
        print(f"数据库 {db_name} 连接成功")

    def create_table(self):
        """创建主营业务构成表"""
        try:
            create_table_sql = '''CREATE TABLE IF NOT EXISTS fina_mainbz_vip (
                ts_code TEXT,
                end_date TEXT,
                bz_item TEXT,
                bz_sales REAL,
                bz_profit REAL,
                bz_cost REAL,
                curr_type TEXT,
                update_flag TEXT,
                type TEXT,
                PRIMARY KEY (ts_code, end_date, bz_item, type)
            )'''
            
            # 首先删除已存在的表
            self.conn.execute('DROP TABLE IF EXISTS fina_mainbz_vip')
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 fina_mainbz_vip 创建成功")
            
        except Exception as e:
            print(f"创建表格 fina_mainbz_vip 时出错: {e}")
            raise

    def insert_data(self, df, type_flag):
        """将DataFrame数据插入到表格中"""
        try:
            # 添加type字段
            df['type'] = type_flag
            
            # 创建临时表
            temp_table_name = 'temp_fina_mainbz_vip'
            df.to_sql(temp_table_name, self.conn, if_exists='replace', index=False)
            
            # 使用INSERT OR REPLACE将数据从临时表插入到主表
            insert_sql = f'''
                INSERT OR REPLACE INTO fina_mainbz_vip 
                SELECT * FROM {temp_table_name}
            '''
            self.conn.execute(insert_sql)
            
            # 删除临时表
            self.conn.execute(f'DROP TABLE IF EXISTS {temp_table_name}')
            
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")
        except Exception as e:
            print(f"写入数据时出错: {e}")
            self.conn.rollback()

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_report_periods():
    """获取近5年的报告期"""
    periods = []
    now = datetime.now()
    
    # 获取最近12个季度（3年）的报告期
    for i in range(12):
        year = now.year - (i // 4)
        quarter = 4 - (i % 4)
        if quarter == 4:
            period = f"{year}1231"
        elif quarter == 3:
            period = f"{year}0930"
        elif quarter == 2:
            period = f"{year}0630"
        else:
            period = f"{year}0331"
        periods.append(period)
    
    return sorted(periods)

def fetch_mainbz_data(period, type_flag):
    """获取单个报告期的主营业务构成数据"""
    try:
        print(f"正在获取 {period} 的主营业务构成数据（{type_flag}）...")
        df = pro.fina_mainbz_vip(period=period, type=type_flag)
        time.sleep(0.5)  # 添加延时以避免频率限制
        return df
    except Exception as e:
        print(f"获取 {period} 的主营业务构成数据时出错: {e}")
        return None

def process_period_data(period, type_flag, db):
    """处理单个报告期的数据"""
    try:
        df = fetch_mainbz_data(period, type_flag)
        if df is not None and not df.empty:
            db.insert_data(df, type_flag)
            print(f"成功获取 {period} 的主营业务构成数据，共 {len(df)} 条记录")
            return True
        return False
    except Exception as e:
        print(f"处理 {period} 的主营业务构成数据时出错: {e}")
        return False

def main():
    # 确保financial_data目录存在
    os.makedirs('financial_data', exist_ok=True)
    
    # 创建数据库实例
    db = StockDatabase()
    
    try:
        # 创建表
        db.create_table()
        
        # 获取报告期列表
        periods = get_report_periods()
        print(f"将获取以下报告期的数据: {periods}")
        
        # 分别获取按产品和按地区的数据
        type_flags = ['P', 'D']
        
        total_periods = len(periods)
        for type_flag in type_flags:
            print(f"\n开始获取按{type_flag}分类的数据...")
            for idx, period in enumerate(periods):
                print(f"正在处理第 {idx+1}/{total_periods} 个报告期: {period}")
                
                if not process_period_data(period, type_flag, db):
                    print(f"跳过 {period} 的处理")
                    continue
                
                # 每处理4个报告期（一年）后额外等待一段时间
                if (idx + 1) % 4 == 0:
                    print(f"已处理 {idx+1} 个报告期，暂停1分钟...")
                    time.sleep(60)
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行A股主营业务构成数据获取程序...")
    main()
    print("程序运行完成！") 