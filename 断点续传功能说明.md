# 断点续传功能说明

## 🎯 **功能概述**

断点续传功能可以让您在数据获取过程中断后，从上次中断的地方继续获取数据，而不需要从头开始，大大节省时间和资源。

## 💾 **进度持久化**

### **自动保存**
- ✅ **程序退出时自动保存**：所有进度信息永久保存到数据库
- ✅ **实时更新**：脚本执行过程中实时保存进度
- ✅ **状态记录**：完整记录每个脚本的执行状态和时间
- ✅ **异常恢复**：程序崩溃后可完整恢复进度

### **持久化数据**
- 📊 **脚本级别进度**：总体执行状态、完成数量、失败数量
- 📈 **股票级别进度**：每只股票的处理状态和时间
- ⏰ **时间戳记录**：最后运行时间、最后更新时间
- 📝 **详细日志**：执行结果、错误信息、备注说明

## 🚀 **主要特性**

### **1. 自动检测**
- 程序启动时自动检测是否有未完成的任务
- 智能识别已获取的数据，避免重复下载
- 支持多种中断情况：程序崩溃、手动停止、网络中断等

### **2. 进度管理**
- 详细记录每个脚本的执行进度
- 跟踪每只股票的处理状态
- 保存失败记录，便于重试

### **3. 用户选择**
- 断点续传：从上次中断处继续
- 重新开始：清除进度，从头获取
- 跳过执行：暂时不执行该脚本

## 📋 **使用方法**

### **1. GUI界面操作**

#### **自动加载历史进度**
- 程序启动时自动显示所有脚本的历史状态
- 状态图标说明：
  - ✅ 已完成：脚本成功执行完毕
  - 🔄 运行中：脚本正在执行
  - ❌ 失败：脚本执行失败
  - ⏸️ 中断：脚本被中断
  - ⚪ 未运行：从未执行过

#### **查看详细进度**
1. 点击主界面的"查看进度"按钮
2. 查看所有脚本的执行状态和进度
3. 了解数据获取的完成情况和时间信息

#### **运行脚本**
1. 选择要运行的脚本
2. 点击"运行选中脚本"或"运行全部脚本"
3. 如果检测到未完成的任务，会弹出断点续传对话框
4. 选择相应的操作：
   - **断点续传**：继续上次的进度
   - **重新开始**：清除进度重新获取
   - **取消**：跳过该脚本

### **2. 断点续传对话框信息**

对话框会显示以下信息：
- **脚本名称**：当前脚本的名称
- **上次运行时间**：最后一次执行的时间
- **任务状态**：completed（完成）、running（运行中）、failed（失败）、partial（部分完成）
- **完成进度**：已完成项目数/总项目数的百分比
- **失败项目**：处理失败的项目数量
- **数据库信息**：已存储数据的时间范围和记录数

## 🔧 **技术实现**

### **1. 进度数据库**
```
gui/progress.db
├── script_progress     # 脚本级别进度
└── stock_progress      # 股票级别进度
```

### **2. 数据存储**
- **脚本进度**：记录脚本的整体执行状态
- **股票进度**：记录每只股票的处理状态
- **时间戳**：记录最后更新时间
- **错误信息**：记录失败原因

### **3. 智能跳过逻辑**
- 检查数据库中已存在的记录
- 根据时间戳判断数据是否需要更新
- 自动跳过最近已处理的股票

## 📊 **支持的脚本类型**

### **完全支持**
- ✅ 市场数据脚本（daily.py, daily_basic.py等）
- ✅ 基础数据脚本（stock_basic.py, stock_company.py等）
- ✅ 财务数据脚本（income.py, balancesheet.py等）

### **部分支持**
- 🔄 实时数据脚本（需要特殊处理）
- 🔄 一次性数据脚本（如股票列表）

## 🎮 **测试功能**

### **基础功能测试**
运行测试脚本来体验断点续传功能：

```bash
python test_resume_feature.py
```

测试功能包括：
1. **模拟数据获取**：模拟真实的数据获取过程
2. **查看进度**：显示所有脚本的进度信息
3. **清除进度**：重置所有进度记录
4. **测试包装器**：测试脚本包装器功能

### **进度持久化测试**
运行进度持久化测试脚本：

```bash
python test_progress_persistence.py
```

测试功能包括：
1. **创建测试数据**：生成各种状态的进度记录
2. **查看进度**：显示详细的进度信息
3. **测试持久化**：验证数据库存储功能
4. **模拟重启**：测试程序重启后的恢复能力
5. **断点续传检测**：验证自动检测功能

## 💡 **使用建议**

### **1. 首次使用**
- 建议先运行少量脚本测试功能
- 观察进度记录是否正确保存
- 熟悉断点续传对话框的操作

### **2. 日常使用**
- 定期查看进度，了解数据获取状态
- 对于长时间运行的任务，可以分批执行
- 网络不稳定时，优先使用断点续传

### **3. 故障处理**
- 如果进度记录异常，可以选择"重新开始"
- 定期备份progress.db文件
- 遇到问题时查看日志信息

## 🔍 **常见问题**

### **Q: 断点续传会跳过哪些数据？**
A: 系统会跳过：
- 最近7天内已成功获取的股票数据
- 数据库中已存在且完整的记录
- 状态为"completed"的处理项目

### **Q: 如何强制重新获取所有数据？**
A: 有两种方法：
1. 在断点续传对话框中选择"重新开始"
2. 手动删除对应的数据库文件

### **Q: 进度记录占用多少空间？**
A: 进度数据库通常只有几MB，对系统影响很小

### **Q: 断点续传是否影响数据完整性？**
A: 不会。系统使用事务机制确保数据一致性，断点续传只是跳过已完成的部分。

### **Q: 程序退出后进度会丢失吗？**
A: 不会。所有进度信息都保存在SQLite数据库中，程序重启后会自动加载历史进度。

### **Q: 如何查看历史运行记录？**
A: 在主界面可以看到每个脚本的状态图标和最后运行时间，点击"查看进度"可以看到详细信息。

## 🎉 **优势总结**

1. **节省时间**：避免重复获取已有数据
2. **提高效率**：支持大规模数据获取任务
3. **增强稳定性**：网络中断或程序崩溃后可快速恢复
4. **用户友好**：直观的进度显示和操作选择
5. **智能化**：自动检测和处理各种中断情况
6. **持久化**：进度信息永久保存，程序重启后完整恢复
7. **可视化**：实时显示历史状态和执行时间

## 🔄 **进度持久化流程**

```
程序启动 → 自动加载历史进度 → 显示状态图标
    ↓
运行脚本 → 检测断点续传 → 用户选择操作
    ↓
执行过程 → 实时保存进度 → 更新状态显示
    ↓
程序退出 → 保存当前状态 → 下次启动恢复
```

通过完整的进度持久化功能，您可以更加高效和稳定地获取股票数据，特别是在处理大量数据或网络环境不稳定的情况下。即使程序意外退出，也能完整恢复之前的工作进度。🚀
