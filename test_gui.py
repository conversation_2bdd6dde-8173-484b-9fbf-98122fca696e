#!/usr/bin/env python3
"""
GUI测试脚本
用于测试GUI的基本功能
"""

import os
import sys

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from gui.script_manager import ScriptManager
        print("✓ ScriptManager 导入成功")
    except ImportError as e:
        print(f"✗ ScriptManager 导入失败: {e}")
        return False
    
    try:
        from gui.database_manager import DatabaseManager
        print("✓ DatabaseManager 导入成功")
    except ImportError as e:
        print(f"✗ DatabaseManager 导入失败: {e}")
        return False
    
    try:
        from gui.thread_manager import ThreadManager
        print("✓ ThreadManager 导入成功")
    except ImportError as e:
        print(f"✗ ThreadManager 导入失败: {e}")
        return False
    
    try:
        from gui.config import get_config, set_config
        print("✓ Config 导入成功")
    except ImportError as e:
        print(f"✗ Config 导入失败: {e}")
        return False
    
    return True

def test_script_manager():
    """测试脚本管理器"""
    print("\n测试脚本管理器...")
    
    try:
        from gui.script_manager import ScriptManager
        manager = ScriptManager()
        
        # 测试获取脚本列表
        scripts = manager.get_all_scripts()
        print(f"✓ 发现 {len(scripts)} 个脚本")
        
        # 显示前5个脚本
        for i, script in enumerate(scripts[:5]):
            print(f"  {i+1}. {script['name']} ({script['module']})")
        
        if len(scripts) > 5:
            print(f"  ... 还有 {len(scripts) - 5} 个脚本")
        
        return True
    except Exception as e:
        print(f"✗ 脚本管理器测试失败: {e}")
        return False

def test_database_manager():
    """测试数据库管理器"""
    print("\n测试数据库管理器...")
    
    try:
        from gui.database_manager import DatabaseManager
        manager = DatabaseManager()
        
        # 测试数据库连接
        if manager.check_connection():
            print("✓ 数据库连接正常")
        else:
            print("⚠ 数据库连接失败（这可能是正常的，如果数据库文件不存在）")
        
        # 测试获取数据库信息
        info = manager.get_database_info()
        if info.get('exists'):
            print(f"✓ 数据库文件存在，大小: {info.get('size_mb', 0):.2f} MB")
            print(f"  表数量: {info.get('table_count', 0)}")
            print(f"  总记录数: {info.get('total_records', 0)}")
        else:
            print("⚠ 数据库文件不存在（首次运行时正常）")
        
        return True
    except Exception as e:
        print(f"✗ 数据库管理器测试失败: {e}")
        return False

def test_config():
    """测试配置管理"""
    print("\n测试配置管理...")
    
    try:
        from gui.config import get_config, set_config, save_config
        
        # 测试获取配置
        max_workers = get_config('execution.max_workers', 4)
        print(f"✓ 获取配置成功，最大并发数: {max_workers}")
        
        # 测试设置配置
        set_config('test.value', 'test_data')
        test_value = get_config('test.value')
        if test_value == 'test_data':
            print("✓ 设置配置成功")
        else:
            print("✗ 设置配置失败")
            return False
        
        # 测试保存配置
        if save_config():
            print("✓ 保存配置成功")
        else:
            print("✗ 保存配置失败")
            return False
        
        return True
    except Exception as e:
        print(f"✗ 配置管理测试失败: {e}")
        return False

def test_gui_creation():
    """测试GUI创建（不显示窗口）"""
    print("\n测试GUI创建...")
    
    try:
        import tkinter as tk
        from gui.main_panel import StockDataGUIPanel
        
        # 创建根窗口但不显示
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 创建GUI实例
        app = StockDataGUIPanel(root)
        print("✓ GUI创建成功")
        
        # 销毁窗口
        root.destroy()
        
        return True
    except Exception as e:
        print(f"✗ GUI创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("股票数据获取GUI管理面板 - 功能测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("脚本管理器", test_script_manager),
        ("数据库管理器", test_database_manager),
        ("配置管理", test_config),
        ("GUI创建", test_gui_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✓ {test_name} 测试通过")
        else:
            print(f"✗ {test_name} 测试失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！GUI应该可以正常运行。")
        print("\n启动GUI:")
        print("  python start_gui.py")
        print("  或")
        print("  python gui/app.py")
    else:
        print("❌ 部分测试失败，请检查错误信息并修复问题。")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
