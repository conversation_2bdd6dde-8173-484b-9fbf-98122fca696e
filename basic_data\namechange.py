import os
import sys
from pathlib import Path
import time

# 获取项目根目录
root_dir = str(Path(__file__).resolve().parent.parent)
sys.path.insert(0, root_dir)

from config import get_token
import tushare as ts
import pandas as pd
from datetime import datetime, timedelta
import sqlite3

# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

class NameChangeDatabase:
    def __init__(self, db_name='data.db'):
        """初始化数据库连接"""
        try:
            # 使用项目根目录的数据库路径
            self.db_path = os.path.join(root_dir, db_name)
            self.conn = sqlite3.connect(self.db_path)
            print(f"数据库 {self.db_path} 连接成功")
        except sqlite3.Error as e:
            print(f"数据库连接错误: {e}")
            raise

    def create_table(self):
        """创建股票曾用名表"""
        try:
            create_table_sql = '''CREATE TABLE IF NOT EXISTS namechange (
                ts_code TEXT,
                name TEXT,
                start_date TEXT,
                end_date TEXT,
                ann_date TEXT,
                change_reason TEXT,
                PRIMARY KEY (ts_code, start_date)
            )'''
            
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 namechange 创建成功")
            
        except Exception as e:
            print(f"创建表格 namechange 时出错: {e}")
            raise

    def get_existing_records(self):
        """获取数据库中已存在的记录"""
        try:
            cursor = self.conn.execute('''
                SELECT ts_code, start_date 
                FROM namechange
            ''')
            return {(row[0], row[1]) for row in cursor.fetchall()}
        except Exception as e:
            print(f"获取已存在记录时出错: {e}")
            return set()

    def insert_data(self, df):
        """将DataFrame数据插入到表格中"""
        try:
            if df is None or df.empty:
                return
                
            # 创建临时表
            temp_table_name = 'temp_namechange'
            df.to_sql(temp_table_name, self.conn, if_exists='replace', index=False)
            
            # 使用INSERT OR REPLACE将数据从临时表插入到主表
            insert_sql = f'''
                INSERT OR REPLACE INTO namechange 
                SELECT * FROM {temp_table_name}
            '''
            self.conn.execute(insert_sql)
            
            # 删除临时表
            self.conn.execute(f'DROP TABLE IF EXISTS {temp_table_name}')
            
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")
        except Exception as e:
            print(f"写入数据时出错: {e}")
            self.conn.rollback()

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_stock_list():
    """获取股票列表"""
    try:
        df = pro.stock_basic(exchange='', list_status='L', 
                            fields='ts_code,symbol,name,area,industry,list_date')
        return df
    except Exception as e:
        print(f"获取股票列表时出错: {e}")
        return None

def fetch_namechange_data(stock_list, start_date, end_date, existing_records):
    """获取股票曾用名数据"""
    try:
        if stock_list is None:
            return None

        # 用于存储所有的曾用名数据
        all_namechange_data = []
        total_stocks = len(stock_list)

        # 获取每只股票的曾用名数据
        for idx, row in stock_list.iterrows():
            ts_code = row['ts_code']
            print(f"正在获取 {ts_code} 的曾用名数据... ({idx + 1}/{total_stocks})")
            
            try:
                df = pro.namechange(ts_code=ts_code,
                                  start_date=start_date,
                                  end_date=end_date,
                                  fields='ts_code,name,start_date,end_date,ann_date,change_reason')
                                  
                if df is not None and not df.empty:
                    # 过滤出新记录
                    df['record_id'] = df.apply(lambda x: (x['ts_code'], x['start_date']), axis=1)
                    new_records = df[~df['record_id'].isin([rec for rec in existing_records])]
                    new_records = new_records.drop('record_id', axis=1)
                    
                    if not new_records.empty:
                        all_namechange_data.append(new_records)
                        print(f"获取到 {len(new_records)} 条新的曾用名记录")
                    else:
                        print("没有新的曾用名记录")
                        
            except Exception as e:
                print(f"获取 {ts_code} 的曾用名数据时出错: {e}")
                continue

            # 添加延时以避免频率限制
            time.sleep(0.3)

        if all_namechange_data:
            # 合并所有股票的曾用名数据
            combined_df = pd.concat(all_namechange_data, ignore_index=True)
            return combined_df
        return None
    except Exception as e:
        print(f"获取曾用名数据时出错: {e}")
        return None

def main():
    # 确保数据目录存在
    os.makedirs('basic_data', exist_ok=True)
    
    # 创建数据库实例
    db = NameChangeDatabase()
    
    try:
        # 创建表（如果不存在）
        db.create_table()
        
        # 获取已存在的记录
        existing_records = db.get_existing_records()
        
        # 计算日期范围（近5年）
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=3*365)).strftime('%Y%m%d')
        
        print(f"获取从 {start_date} 到 {end_date} 的股票曾用名数据...")
        
        # 获取股票列表
        stock_list = get_stock_list()
        if stock_list is None:
            print("未获取到股票列表，程序退出")
            return
            
        # 获取曾用名数据
        namechange_df = fetch_namechange_data(stock_list, start_date, end_date, existing_records)
        
        if namechange_df is not None and not namechange_df.empty:
            # 保存新数据
            db.insert_data(namechange_df)
            print(f"共获取到 {len(namechange_df)} 条新的股票曾用名记录")
        else:
            print("没有新的股票曾用名记录需要更新")
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行股票曾用名数据获取程序...")
    main()
    print("程序运行完成！") 