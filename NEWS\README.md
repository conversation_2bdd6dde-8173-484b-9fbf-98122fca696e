# 股票市场新闻采集工具

## 功能介绍

这是一个用于采集和阅读股票市场新闻的图形界面工具，可以从多个主要财经网站获取最新的股票相关新闻，并将数据保存到项目根目录的SQLite数据库中。

### 主要功能

1. **多源新闻采集**：支持从多个财经网站获取新闻
2. **新闻内容提取**：自动提取新闻正文内容
3. **数据库存储**：将采集的新闻保存到SQLite数据库
4. **历史记录查看**：支持查看历史采集的新闻
5. **关键词搜索**：支持根据关键词搜索新闻
6. **原文链接打开**：支持在浏览器中打开新闻原文

### 支持的新闻源

- 凤凰网财经
- 金融界
- 同花顺
- 新浪财经焦点
- 云财经
- 东方财富
- 华尔街见闻

## 使用方法

1. **启动程序**
   ```
   python stock_news_crawler.py
   ```
   或者使用启动脚本：
   ```
   python run_news_crawler.py
   ```

2. **选择新闻源**：从顶部下拉菜单选择需要采集的新闻源

3. **开始采集**：点击"开始采集"按钮，程序将从选中的新闻源获取最新新闻

4. **查看新闻**：
   - 在上方的列表中点击某条新闻，下方会显示新闻的详细内容
   - 点击"打开原文链接"可在浏览器中查看原始新闻页面

5. **保存新闻**：点击"保存到数据库"按钮，将当前显示的所有新闻保存到SQLite数据库中

6. **查看历史记录**：点击"查看历史记录"按钮，加载已经保存到数据库中的新闻

7. **搜索新闻**：在搜索框中输入关键词，点击"搜索"按钮，搜索数据库中符合条件的新闻

8. **清除内容**：点击"清除内容"按钮，清除当前显示的所有内容

## 数据库结构

新闻数据存储在项目根目录的`data.db`文件中的`news`表中，表结构如下：

| 字段名      | 类型    | 说明                     |
|------------|---------|-------------------------|
| id         | INTEGER | 主键，自动递增            |
| title      | TEXT    | 新闻标题                 |
| date       | TEXT    | 发布日期                 |
| source     | TEXT    | 新闻来源                 |
| url        | TEXT    | 新闻原文链接              |
| content    | TEXT    | 新闻正文内容              |
| fetch_time | TEXT    | 采集时间                 |

## 文件结构

- `stock_news_crawler.py`：主程序，包含GUI界面
- `news_parser.py`：新闻解析模块，处理不同新闻源的网页解析
- `db_manager.py`：数据库管理模块，处理新闻的存储和检索
- `run_news_crawler.py`：启动脚本，自动安装依赖并启动程序
- `requirements.txt`：依赖库列表
- `README.md`：说明文档

## 依赖库

- tkinter：GUI界面
- requests：网络请求
- beautifulsoup4：HTML解析
- webbrowser：打开浏览器
- json：JSON数据处理
- sqlite3：数据库操作

## 安装依赖

```
pip install requests beautifulsoup4
```

## 注意事项

1. 由于网站结构可能变化，解析器可能需要定期更新以适应新的网页结构
2. 部分网站可能有反爬措施，可能需要增加更多的请求头信息或代理
3. 新闻数据存储在项目根目录的`data.db`文件中，可以直接使用SQLite工具查看

## 未来改进

1. 添加定时自动采集功能
2. 增加更多新闻源支持
3. 优化数据库结构，添加更多的索引和查询功能
4. 实现新闻分类和标记功能
5. 添加数据可视化功能 