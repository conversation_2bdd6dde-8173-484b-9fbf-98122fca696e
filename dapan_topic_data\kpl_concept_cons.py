import os
import sys
import time
import pandas as pd
import sqlite3
import tushare as ts
from datetime import datetime, timedelta
try:
    from dateutil.relativedelta import relativedelta
except ImportError:
    print("请安装python-dateutil包：pip install python-dateutil")
    sys.exit(1)

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

from config import get_token

# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

class KplConceptConsDatabase:
    def __init__(self, db_path=None, max_retries=3):
        """初始化数据库连接"""
        if db_path is None:
            # 使用项目根目录下的data.db
            db_path = os.path.join(root_dir, 'data.db')
        self.db_name = db_path
        self.conn = None
        
        # 尝试连接数据库
        for retry in range(max_retries):
            try:
                self.conn = sqlite3.connect(db_path)
                print(f"数据库 {db_path} 连接成功")
                break
            except sqlite3.Error as e:
                if retry < max_retries - 1:
                    print(f"数据库连接失败: {e}，将在3秒后重试（第{retry + 1}次）...")
                    time.sleep(3)
                else:
                    print(f"数据库连接失败: {e}")
                    raise
        
        if self.conn is None:
            raise sqlite3.Error("无法连接到数据库")

    def create_table(self):
        """创建开盘啦题材成分表"""
        try:
            create_table_sql = '''CREATE TABLE IF NOT EXISTS kpl_concept_cons (
                trade_date TEXT,
                ts_code TEXT,
                name TEXT,
                con_name TEXT,
                con_code TEXT,
                desc TEXT,
                hot_num INTEGER,
                PRIMARY KEY (trade_date, ts_code, con_code)
            )'''
            
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 kpl_concept_cons 创建成功")
            
        except Exception as e:
            print(f"创建表格 kpl_concept_cons 时出错: {e}")
            raise

    def get_existing_records(self):
        """获取数据库中已存在的记录"""
        try:
            cursor = self.conn.execute('''
                SELECT trade_date, ts_code, con_code 
                FROM kpl_concept_cons
            ''')
            return {(row[0], row[1], row[2]) for row in cursor.fetchall()}
        except Exception as e:
            print(f"获取已存在记录时出错: {e}")
            return set()

    def insert_data(self, df):
        """将DataFrame数据插入到表格中"""
        try:
            if df is None or df.empty:
                return
                
            # 创建临时表
            temp_table_name = 'temp_kpl_concept_cons'
            df.to_sql(temp_table_name, self.conn, if_exists='replace', index=False)
            
            # 使用INSERT OR REPLACE将数据从临时表插入到主表
            insert_sql = f'''
                INSERT OR REPLACE INTO kpl_concept_cons 
                SELECT * FROM {temp_table_name}
            '''
            self.conn.execute(insert_sql)
            
            # 删除临时表
            self.conn.execute(f'DROP TABLE IF EXISTS {temp_table_name}')
            
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")
        except Exception as e:
            print(f"写入数据时出错: {e}")
            self.conn.rollback()

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_trading_dates(start_date, end_date):
    """获取交易日历"""
    try:
        df = pro.trade_cal(exchange='', start_date=start_date, end_date=end_date,
                          fields='cal_date,is_open')
        trade_dates = df[df['is_open'] == 1]['cal_date'].tolist()
        return trade_dates
    except Exception as e:
        print(f"获取交易日历时出错: {e}")
        return []

def get_concept_list(trade_date):
    """获取指定日期的题材列表"""
    try:
        print(f"正在获取 {trade_date} 的题材列表...")
        df = pro.kpl_concept(trade_date=trade_date)
        if df is None or df.empty:
            print(f"警告：{trade_date} 没有返回任何题材数据")
            return []
        print(f"成功获取到 {len(df)} 个题材")
        return df['ts_code'].tolist()
    except Exception as e:
        print(f"获取题材列表时出错: {str(e)}")
        if "抱歉，您没有访问该接口的权限" in str(e):
            print("提示：请检查是否开通了开盘啦数据接口的权限")
        elif "频次限制" in str(e):
            print("提示：接口调用频率过高，请增加延时")
            time.sleep(1)  # 增加延时
        return []

def fetch_concept_cons_data(trade_date, ts_code, existing_records):
    """获取指定日期和题材代码的成分股数据"""
    try:
        print(f"正在获取 {trade_date} 题材 {ts_code} 的成分股数据...")
        df = pro.kpl_concept_cons(trade_date=trade_date, ts_code=ts_code)
        
        if df is not None and not df.empty:
            # 过滤出新记录
            df['record_id'] = df.apply(lambda x: (x['trade_date'], x['ts_code'], x['con_code']), axis=1)
            new_records = df[~df['record_id'].isin([rec for rec in existing_records])]
            new_records = new_records.drop('record_id', axis=1)
            
            if not new_records.empty:
                print(f"获取到 {len(new_records)} 条新的成分股记录")
                return new_records
            else:
                print("没有新的成分股记录")
                return None
                
        time.sleep(0.3)  # 添加延时以避免频率限制
        return None
    except Exception as e:
        print(f"获取成分股数据时出错: {e}")
        return None

def main():
    # 确保数据目录存在
    os.makedirs(os.path.dirname(os.path.join(root_dir, 'data.db')), exist_ok=True)
    
    # 创建数据库实例
    db = KplConceptConsDatabase()
    
    try:
        # 创建表（如果不存在）
        db.create_table()
        
        # 获取已存在的记录
        existing_records = db.get_existing_records()
        
        # 计算日期范围（近5年）
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=3*365)).strftime('%Y%m%d')
        
        print(f"获取从 {start_date} 到 {end_date} 的开盘啦题材成分股数据...")
        
        # 获取交易日历
        trade_dates = get_trading_dates(start_date, end_date)
        if not trade_dates:
            print("未获取到交易日历，程序退出")
            return
            
        # 获取开盘啦题材成分股数据
        all_data = []
        total_dates = len(trade_dates)
        
        for idx, trade_date in enumerate(trade_dates, 1):
            print(f"处理第 {idx}/{total_dates} 个交易日: {trade_date}")
            
            # 获取当日所有题材列表
            concept_list = get_concept_list(trade_date)
            if not concept_list:
                print(f"跳过 {trade_date} 的处理：未找到题材列表")
                continue
            
            # 由于单次最大3000条限制，需要按题材代码分批获取
            for ts_code in concept_list:
                df = fetch_concept_cons_data(trade_date, ts_code, existing_records)
                if df is not None and not df.empty:
                    all_data.append(df)
                time.sleep(0.3)  # 添加延时以避免频率限制
        
        if all_data:
            # 合并所有数据
            combined_df = pd.concat(all_data, ignore_index=True)
            # 保存新数据
            db.insert_data(combined_df)
            print(f"共获取到 {len(combined_df)} 条新的开盘啦题材成分股记录")
        else:
            print("没有新的开盘啦题材成分股记录需要更新")
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行开盘啦题材成分股数据获取程序...")
    main()
    print("程序运行完成！") 