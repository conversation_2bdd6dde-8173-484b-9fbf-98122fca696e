# 数据获取模块: stock_basic
import sqlite3
import pandas as pd
import logging
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

# 导入现有的股票基本信息获取模块
from basic_data.stock_data import fetch_stock_basic_data, StockBasicDatabase

logger = logging.getLogger(f"DataFetcher.{__name__}")

def get_tables():
    """返回此模块可以处理的表名列表"""
    return ["stock_basic"]

def fetch_data(table_name, db_path):
    """获取股票基本信息并存入数据库
    
    Args:
        table_name: 表名
        db_path: 数据库路径
        
    Returns:
        dict: 包含处理结果信息的字典
    """
    if table_name != "stock_basic":
        return {"success": False, "message": f"此模块不处理表 {table_name}"}
    
    try:
        logger.info("正在获取股票基本信息...")
        
        # 使用现有的StockBasicDatabase类
        db = StockBasicDatabase(db_path)
        
        try:
            # 创建表（如果不存在）
            db.create_table()
            
            # 获取已存在的记录
            existing_records = db.get_existing_records()
            
            # 获取股票基本信息数据
            stock_df = fetch_stock_basic_data(existing_records)
            
            if stock_df is not None and not stock_df.empty:
                # 保存新数据
                db.insert_data(stock_df)
                logger.info(f"成功获取股票基本信息，共 {len(stock_df)} 条记录")
                return {"success": True, "message": f"成功获取股票基本信息", "count": len(stock_df)}
            else:
                logger.info("没有新的股票基本信息记录需要更新")
                return {"success": True, "message": "没有新的股票基本信息记录需要更新", "count": 0}
        finally:
            # 确保数据库连接被关闭
            db.close()
            
    except Exception as e:
        logger.error(f"获取股票基本信息时出错: {e}")
        return {"success": False, "message": str(e)} 