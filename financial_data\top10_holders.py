import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)



# 添加项目根目录到Python路径

from config import get_token
import tushare as ts
import pandas as pd
import sqlite3
import time
from datetime import datetime, timedelta
# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

class StockDatabase:
    def __init__(self, db_name='data.db'):
        """初始化数据库连接"""
        self.db_name = db_name
        self.conn = sqlite3.connect(db_name)
        print(f"数据库 {db_name} 连接成功")

    def create_table(self):
        """创建前十大股东表"""
        try:
            create_table_sql = '''CREATE TABLE IF NOT EXISTS top10_holders (
                ts_code TEXT,
                ann_date TEXT,
                end_date TEXT,
                holder_name TEXT,
                hold_amount FLOAT,
                hold_ratio FLOAT,
                hold_float_ratio FLOAT,
                hold_change FLOAT,
                holder_type TEXT,
                PRIMARY KEY (ts_code, end_date, holder_name)
            )'''
            
            # 首先删除已存在的表
            self.conn.execute('DROP TABLE IF EXISTS top10_holders')
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 top10_holders 创建成功")
            
        except Exception as e:
            print(f"创建表格 top10_holders 时出错: {e}")
            raise

    def insert_data(self, df):
        """将DataFrame数据插入到表格中"""
        try:
            # 创建临时表
            temp_table_name = 'temp_top10_holders'
            df.to_sql(temp_table_name, self.conn, if_exists='replace', index=False)
            
            # 使用INSERT OR REPLACE将数据从临时表插入到主表
            insert_sql = f'''
                INSERT OR REPLACE INTO top10_holders 
                SELECT * FROM {temp_table_name}
            '''
            self.conn.execute(insert_sql)
            
            # 删除临时表
            self.conn.execute(f'DROP TABLE IF EXISTS {temp_table_name}')
            
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")
        except Exception as e:
            print(f"写入数据时出错: {e}")
            self.conn.rollback()

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_stock_list():
    """获取股票列表"""
    try:
        df = pro.stock_basic(exchange='', list_status='L')
        return df['ts_code'].tolist()
    except Exception as e:
        print(f"获取股票列表时出错: {e}")
        return []

def get_report_periods():
    """获取近5年的报告期"""
    periods = []
    now = datetime.now()
    
    # 获取最近12个季度（3年）的报告期
    for i in range(12):
        year = now.year - (i // 4)
        quarter = 4 - (i % 4)
        if quarter == 4:
            period = f"{year}1231"
        elif quarter == 3:
            period = f"{year}0930"
        elif quarter == 2:
            period = f"{year}0630"
        else:
            period = f"{year}0331"
        periods.append(period)
    
    return sorted(periods)

def fetch_holder_data(ts_code, period):
    """获取单个股票单个报告期的前十大股东数据"""
    try:
        print(f"正在获取 {ts_code} 在 {period} 的前十大股东数据...")
        df = pro.top10_holders(ts_code=ts_code, period=period)
        time.sleep(0.5)  # 添加延时以避免频率限制
        return df
    except Exception as e:
        print(f"获取 {ts_code} 在 {period} 的前十大股东数据时出错: {e}")
        return None

def process_stock_data(ts_code, period, db):
    """处理单个股票单个报告期的数据"""
    try:
        df = fetch_holder_data(ts_code, period)
        if df is not None and not df.empty:
            db.insert_data(df)
            print(f"成功获取 {ts_code} 在 {period} 的前十大股东数据，共 {len(df)} 条记录")
            return True
        return False
    except Exception as e:
        print(f"处理 {ts_code} 在 {period} 的前十大股东数据时出错: {e}")
        return False

def main():
    # 确保financial_data目录存在
    os.makedirs('financial_data', exist_ok=True)
    
    # 创建数据库实例
    db = StockDatabase()
    
    try:
        # 创建表
        db.create_table()
        
        # 获取股票列表
        stock_list = get_stock_list()
        if not stock_list:
            print("获取股票列表失败，程序退出")
            return
        
        # 获取报告期列表
        periods = get_report_periods()
        print(f"将获取以下报告期的数据: {periods}")
        
        total_stocks = len(stock_list)
        for stock_idx, ts_code in enumerate(stock_list):
            print(f"正在处理第 {stock_idx+1}/{total_stocks} 个股票: {ts_code}")
            
            for period_idx, period in enumerate(periods):
                if not process_stock_data(ts_code, period, db):
                    print(f"跳过 {ts_code} 在 {period} 的处理")
                    continue
                
                # 每处理一个股票的4个季度后暂停15秒
                if (period_idx + 1) % 4 == 0:
                    print(f"已处理 {ts_code} 的 {period_idx+1} 个季度，暂停15秒...")
                    time.sleep(15)
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行A股前十大股东数据获取程序...")
    main()
    print("程序运行完成！") 