#!/usr/bin/env python3
"""
断点续传管理器 - 记录和恢复数据获取进度
"""

import os
import json
import sqlite3
from datetime import datetime, timedelta
import pandas as pd

class ResumeManager:
    """断点续传管理器"""
    
    def __init__(self, progress_db_path="gui/progress.db"):
        self.progress_db_path = progress_db_path
        self.init_progress_db()
    
    def init_progress_db(self):
        """初始化进度数据库"""
        os.makedirs(os.path.dirname(self.progress_db_path), exist_ok=True)
        
        conn = sqlite3.connect(self.progress_db_path)
        cursor = conn.cursor()
        
        # 创建脚本进度表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS script_progress (
                script_name TEXT PRIMARY KEY,
                last_run_time TEXT,
                status TEXT,  -- 'completed', 'running', 'failed', 'partial'
                progress_data TEXT,  -- JSON格式的进度数据
                total_items INTEGER,
                completed_items INTEGER,
                failed_items INTEGER,
                last_processed_item TEXT,
                notes TEXT
            )
        ''')
        
        # 创建股票处理进度表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_progress (
                script_name TEXT,
                ts_code TEXT,
                last_date TEXT,
                status TEXT,  -- 'completed', 'partial', 'failed'
                record_count INTEGER,
                last_update_time TEXT,
                PRIMARY KEY (script_name, ts_code)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def save_script_progress(self, script_name, status, progress_data=None, 
                           total_items=0, completed_items=0, failed_items=0,
                           last_processed_item=None, notes=None):
        """保存脚本进度"""
        conn = sqlite3.connect(self.progress_db_path)
        cursor = conn.cursor()
        
        progress_json = json.dumps(progress_data) if progress_data else None
        
        cursor.execute('''
            INSERT OR REPLACE INTO script_progress 
            (script_name, last_run_time, status, progress_data, total_items, 
             completed_items, failed_items, last_processed_item, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (script_name, datetime.now().isoformat(), status, progress_json,
              total_items, completed_items, failed_items, last_processed_item, notes))
        
        conn.commit()
        conn.close()
    
    def get_script_progress(self, script_name):
        """获取脚本进度"""
        conn = sqlite3.connect(self.progress_db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM script_progress WHERE script_name = ?
        ''', (script_name,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'script_name': result[0],
                'last_run_time': result[1],
                'status': result[2],
                'progress_data': json.loads(result[3]) if result[3] else None,
                'total_items': result[4],
                'completed_items': result[5],
                'failed_items': result[6],
                'last_processed_item': result[7],
                'notes': result[8]
            }
        return None
    
    def save_stock_progress(self, script_name, ts_code, last_date, status, record_count=0):
        """保存股票处理进度"""
        conn = sqlite3.connect(self.progress_db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO stock_progress 
            (script_name, ts_code, last_date, status, record_count, last_update_time)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (script_name, ts_code, last_date, status, record_count, 
              datetime.now().isoformat()))
        
        conn.commit()
        conn.close()
    
    def get_stock_progress(self, script_name, ts_code=None):
        """获取股票处理进度"""
        conn = sqlite3.connect(self.progress_db_path)
        cursor = conn.cursor()
        
        if ts_code:
            cursor.execute('''
                SELECT * FROM stock_progress WHERE script_name = ? AND ts_code = ?
            ''', (script_name, ts_code))
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'script_name': result[0],
                    'ts_code': result[1],
                    'last_date': result[2],
                    'status': result[3],
                    'record_count': result[4],
                    'last_update_time': result[5]
                }
        else:
            cursor.execute('''
                SELECT * FROM stock_progress WHERE script_name = ?
            ''', (script_name,))
            results = cursor.fetchall()
            conn.close()
            
            return [{
                'script_name': row[0],
                'ts_code': row[1],
                'last_date': row[2],
                'status': row[3],
                'record_count': row[4],
                'last_update_time': row[5]
            } for row in results]
        
        return None
    
    def get_completed_stocks(self, script_name):
        """获取已完成的股票列表"""
        conn = sqlite3.connect(self.progress_db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT ts_code FROM stock_progress 
            WHERE script_name = ? AND status = 'completed'
        ''', (script_name,))
        
        results = cursor.fetchall()
        conn.close()
        
        return [row[0] for row in results]
    
    def get_resume_point(self, script_name):
        """获取断点续传点"""
        progress = self.get_script_progress(script_name)
        if not progress:
            return None
        
        completed_stocks = self.get_completed_stocks(script_name)
        
        return {
            'last_run_time': progress['last_run_time'],
            'status': progress['status'],
            'completed_stocks': completed_stocks,
            'total_items': progress['total_items'],
            'completed_items': progress['completed_items'],
            'last_processed_item': progress['last_processed_item']
        }
    
    def should_skip_stock(self, script_name, ts_code, check_recent_days=7):
        """判断是否应该跳过某只股票"""
        stock_progress = self.get_stock_progress(script_name, ts_code)
        
        if not stock_progress:
            return False
        
        # 如果状态是完成，检查是否需要更新
        if stock_progress['status'] == 'completed':
            last_update = datetime.fromisoformat(stock_progress['last_update_time'])
            if datetime.now() - last_update < timedelta(days=check_recent_days):
                return True
        
        return False
    
    def clear_script_progress(self, script_name):
        """清除脚本进度（重新开始）"""
        conn = sqlite3.connect(self.progress_db_path)
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM script_progress WHERE script_name = ?', (script_name,))
        cursor.execute('DELETE FROM stock_progress WHERE script_name = ?', (script_name,))
        
        conn.commit()
        conn.close()
    
    def get_all_progress(self):
        """获取所有脚本的进度信息"""
        conn = sqlite3.connect(self.progress_db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT script_name, status, total_items, completed_items, 
                   failed_items, last_run_time
            FROM script_progress
            ORDER BY last_run_time DESC
        ''')
        
        results = cursor.fetchall()
        conn.close()
        
        return [{
            'script_name': row[0],
            'status': row[1],
            'total_items': row[2],
            'completed_items': row[3],
            'failed_items': row[4],
            'last_run_time': row[5],
            'progress_percent': round((row[3] / row[2] * 100) if row[2] > 0 else 0, 1)
        } for row in results]

class DataExistenceChecker:
    """数据存在性检查器"""
    
    @staticmethod
    def check_table_exists(db_path, table_name):
        """检查表是否存在"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute('''
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            ''', (table_name,))
            result = cursor.fetchone()
            conn.close()
            return result is not None
        except:
            return False
    
    @staticmethod
    def get_data_date_range(db_path, table_name, ts_code=None):
        """获取数据的日期范围"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            if ts_code:
                cursor.execute(f'''
                    SELECT MIN(trade_date), MAX(trade_date), COUNT(*)
                    FROM {table_name} WHERE ts_code = ?
                ''', (ts_code,))
            else:
                cursor.execute(f'''
                    SELECT MIN(trade_date), MAX(trade_date), COUNT(*)
                    FROM {table_name}
                ''')
            
            result = cursor.fetchone()
            conn.close()
            
            if result and result[0]:
                return {
                    'start_date': result[0],
                    'end_date': result[1],
                    'record_count': result[2]
                }
        except Exception as e:
            print(f"检查数据范围时出错: {e}")
        
        return None
    
    @staticmethod
    def get_missing_stocks(db_path, table_name, stock_list):
        """获取缺失数据的股票列表"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 获取已有数据的股票
            cursor.execute(f'SELECT DISTINCT ts_code FROM {table_name}')
            existing_stocks = set(row[0] for row in cursor.fetchall())
            conn.close()
            
            # 计算缺失的股票
            all_stocks = set(stock_list)
            missing_stocks = all_stocks - existing_stocks
            
            return list(missing_stocks)
        except:
            return stock_list  # 如果出错，返回全部股票列表

def main():
    """测试函数"""
    manager = ResumeManager()
    
    # 测试保存进度
    manager.save_script_progress(
        script_name="daily.py",
        status="running",
        total_items=100,
        completed_items=50,
        last_processed_item="000001.SZ"
    )
    
    # 测试获取进度
    progress = manager.get_script_progress("daily.py")
    print("脚本进度:", progress)
    
    # 测试获取所有进度
    all_progress = manager.get_all_progress()
    print("所有进度:", all_progress)

if __name__ == "__main__":
    main()
