import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)



# 添加项目根目录到Python路径

from config import get_token
import tushare as ts
import pandas as pd
from datetime import datetime, timedelta
import sqlite3
import time
# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

class StockDatabase:
    def __init__(self, db_name='data.db'):
        """初始化数据库连接"""
        self.db_name = db_name
        self.conn = sqlite3.connect(db_name)
        print(f"数据库 {db_name} 连接成功")

    def create_table(self, table_name, df):
        """根据DataFrame创建表格"""
        # 获取列名和数据类型
        columns = []
        for col in df.columns:
            dtype = df[col].dtype
            if 'int' in str(dtype):
                sql_type = 'INTEGER'
            elif 'float' in str(dtype):
                sql_type = 'REAL'
            else:
                sql_type = 'TEXT'
            columns.append(f'"{col}" {sql_type}')
        
        # 创建表格
        create_table_sql = f'''CREATE TABLE IF NOT EXISTS {table_name} (
            {', '.join(columns)}
        )'''
        
        try:
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print(f"表格 {table_name} 创建成功")
        except Exception as e:
            print(f"创建表格 {table_name} 时出错: {e}")

    def insert_data(self, table_name, df):
        """将DataFrame数据插入到表格中"""
        try:
            # 将DataFrame写入SQLite数据库
            df.to_sql(table_name, self.conn, if_exists='replace', index=False)
            print(f"数据成功写入表格 {table_name}")
        except Exception as e:
            print(f"写入数据到表格 {table_name} 时出错: {e}")

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_stock_list():
    """获取股票列表"""
    try:
        df = pro.stock_basic(exchange='', list_status='L', 
                            fields='ts_code,symbol,name,area,industry,list_date')
        return df
    except Exception as e:
        print(f"获取股票列表时出错: {e}")
        return None

def fetch_daily_data():
    """获取A股日线行情数据"""
    try:
        # 计算日期范围（近5年）
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=3*365)).strftime('%Y%m%d')
        
        # 获取股票列表
        stock_list = get_stock_list()
        if stock_list is None:
            return None

        # 用于存储所有数据
        all_data = []
        total_stocks = len(stock_list)

        # 每次处理的股票数量（考虑到单次调用限制6000条）
        batch_size = 10  # 由于每只股票获取5年数据约1250条，每批10只股票大约5000条
        
        # 分批处理股票
        for i in range(0, total_stocks, batch_size):
            batch_stocks = stock_list.iloc[i:i+batch_size]
            batch_codes = ','.join(batch_stocks['ts_code'].tolist())
            
            print(f"正在获取第 {i+1} 到 {min(i+batch_size, total_stocks)} 只股票的日线数据...")
            
            try:
                df = pro.daily(ts_code=batch_codes,
                             start_date=start_date,
                             end_date=end_date)
                if df is not None and not df.empty:
                    all_data.append(df)
                    print(f"成功获取到 {len(df)} 条记录")
                
                # 由于每分钟最多调用500次，加入小延时
                time.sleep(0.15)
                
            except Exception as e:
                print(f"获取 {batch_codes} 的日线数据时出错: {e}")
                continue

        if all_data:
            # 合并所有数据
            combined_df = pd.concat(all_data, ignore_index=True)
            # 按日期和股票代码排序
            combined_df = combined_df.sort_values(['trade_date', 'ts_code'])
            return combined_df
        return None
    except Exception as e:
        print(f"获取日线行情数据时出错: {e}")
        return None

def main():
    # 确保market_data目录存在
    os.makedirs('market_data', exist_ok=True)
    
    # 创建数据库实例
    db = StockDatabase()
    
    try:
        print("开始获取A股日线行情数据...")
        
        # 获取日线行情数据
        daily_df = fetch_daily_data()
        
        if daily_df is not None and not daily_df.empty:
            # 创建表并保存数据
            db.create_table('daily', daily_df)
            db.insert_data('daily', daily_df)
            print(f"共获取到 {len(daily_df)} 条日线行情记录")
        else:
            print("未获取到日线行情数据")
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行A股日线行情数据获取程序...")
    main()
    print("程序运行完成！") 