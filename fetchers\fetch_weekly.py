# 数据获取模块: weekly
import sqlite3
import pandas as pd
import logging
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

# 导入现有的周线行情数据获取模块
from market_data.weekly import get_stock_list, fetch_weekly_data, StockDatabase, APIRateLimiter
from datetime import datetime, timedelta

logger = logging.getLogger(f"DataFetcher.{__name__}")

def get_tables():
    """返回此模块可以处理的表名列表"""
    return ["weekly"]

def fetch_data(table_name, db_path):
    """获取周线行情数据并存入数据库
    
    Args:
        table_name: 表名
        db_path: 数据库路径
        
    Returns:
        dict: 包含处理结果信息的字典
    """
    if table_name != "weekly":
        return {"success": False, "message": f"此模块不处理表 {table_name}"}
    
    try:
        logger.info("正在获取周线行情数据...")
        
        # 创建数据库实例和频率限制器
        db = StockDatabase(db_path)
        rate_limiter = APIRateLimiter(max_calls_per_minute=2)
        
        try:
            # 创建表
            db.create_table()
            
            # 获取股票列表
            stock_list = get_stock_list()
            if stock_list is None:
                return {"success": False, "message": "获取股票列表失败"}
            
            # 设置时间范围（近3年）
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=3*365)).strftime('%Y%m%d')
            
            total_stocks = len(stock_list)
            processed_count = 0
            record_count = 0
            
            # 限制处理的股票数量，避免一次处理太多
            max_stocks = min(20, total_stocks)  # 每次最多处理20只股票
            
            for idx, row in stock_list.head(max_stocks).iterrows():
                ts_code = row['ts_code']
                logger.info(f"正在处理第 {idx+1}/{max_stocks} 只股票: {ts_code}")
                
                try:
                    # 获取周线数据
                    df = fetch_weekly_data(ts_code, start_date, end_date, rate_limiter)
                    if df is not None and not df.empty:
                        db.insert_data(df)
                        logger.info(f"成功获取 {ts_code} 的周线数据，共 {len(df)} 条记录")
                        record_count += len(df)
                        processed_count += 1
                except Exception as e:
                    logger.error(f"处理 {ts_code} 时出错: {e}")
                    continue
            
            return {"success": True, "message": f"成功获取周线行情数据，处理了 {processed_count}/{max_stocks} 只股票", "count": record_count}
        finally:
            # 确保数据库连接被关闭
            db.close()
            
    except Exception as e:
        logger.error(f"获取周线行情数据时出错: {e}")
        return {"success": False, "message": str(e)}
