import sqlite3
import os
from datetime import datetime

class DatabaseManager:
    """数据库管理器，负责数据库连接和状态检查"""
    
    def __init__(self, db_path='data.db'):
        # 获取项目根目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        root_dir = os.path.dirname(current_dir)
        
        # 数据库路径
        if not os.path.isabs(db_path):
            self.db_path = os.path.join(root_dir, db_path)
        else:
            self.db_path = db_path
            
    def check_connection(self):
        """检查数据库连接"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            conn.close()
            return True
        except Exception as e:
            print(f"数据库连接检查失败: {e}")
            return False
    
    def get_database_info(self):
        """获取数据库基本信息"""
        try:
            if not os.path.exists(self.db_path):
                return {
                    'exists': False,
                    'size': 0,
                    'tables': [],
                    'total_records': 0,
                    'error': '数据库文件不存在'
                }
            
            # 获取文件大小
            file_size = os.path.getsize(self.db_path)
            
            # 获取表信息
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = [row[0] for row in cursor.fetchall()]
            
            # 获取每个表的记录数
            table_info = []
            total_records = 0
            
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM [{table}]")
                    count = cursor.fetchone()[0]
                    total_records += count
                    
                    # 获取表结构信息
                    cursor.execute(f"PRAGMA table_info([{table}])")
                    columns = cursor.fetchall()
                    
                    table_info.append({
                        'name': table,
                        'records': count,
                        'columns': len(columns),
                        'column_info': columns
                    })
                except sqlite3.Error as e:
                    table_info.append({
                        'name': table,
                        'records': 0,
                        'columns': 0,
                        'error': str(e)
                    })
            
            conn.close()
            
            return {
                'exists': True,
                'path': self.db_path,
                'size': file_size,
                'size_mb': round(file_size / (1024 * 1024), 2),
                'tables': table_info,
                'table_count': len(tables),
                'total_records': total_records,
                'last_modified': datetime.fromtimestamp(os.path.getmtime(self.db_path)).strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            return {
                'exists': False,
                'error': str(e),
                'tables': [],
                'total_records': 0
            }
    
    def get_empty_tables(self):
        """获取空表列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = [row[0] for row in cursor.fetchall()]
            
            empty_tables = []
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM [{table}]")
                    count = cursor.fetchone()[0]
                    if count == 0:
                        empty_tables.append(table)
                except sqlite3.Error:
                    # 如果查询失败，也认为是空表
                    empty_tables.append(table)
            
            conn.close()
            return empty_tables
            
        except Exception as e:
            print(f"获取空表列表失败: {e}")
            return []
    
    def get_table_info(self, table_name):
        """获取指定表的详细信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if not cursor.fetchone():
                return {'exists': False, 'error': '表不存在'}
            
            # 获取记录数
            cursor.execute(f"SELECT COUNT(*) FROM [{table_name}]")
            record_count = cursor.fetchone()[0]
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info([{table_name}])")
            columns = cursor.fetchall()
            
            # 获取最近的记录（如果有的话）
            recent_records = []
            if record_count > 0:
                try:
                    cursor.execute(f"SELECT * FROM [{table_name}] LIMIT 5")
                    recent_records = cursor.fetchall()
                except sqlite3.Error:
                    pass
            
            conn.close()
            
            return {
                'exists': True,
                'name': table_name,
                'records': record_count,
                'columns': [{'name': col[1], 'type': col[2], 'notnull': col[3], 'pk': col[5]} for col in columns],
                'recent_records': recent_records
            }
            
        except Exception as e:
            return {'exists': False, 'error': str(e)}
    
    def create_database_if_not_exists(self):
        """如果数据库不存在则创建"""
        try:
            if not os.path.exists(self.db_path):
                # 确保目录存在
                os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
                
                # 创建空数据库
                conn = sqlite3.connect(self.db_path)
                conn.close()
                return True, "数据库创建成功"
            else:
                return True, "数据库已存在"
        except Exception as e:
            return False, f"创建数据库失败: {str(e)}"
    
    def backup_database(self, backup_path=None):
        """备份数据库"""
        try:
            if not backup_path:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_path = f"{self.db_path}.backup_{timestamp}"
            
            if not os.path.exists(self.db_path):
                return False, "源数据库不存在"
            
            # 使用SQLite的备份API
            source_conn = sqlite3.connect(self.db_path)
            backup_conn = sqlite3.connect(backup_path)
            
            source_conn.backup(backup_conn)
            
            source_conn.close()
            backup_conn.close()
            
            return True, f"数据库备份成功: {backup_path}"
            
        except Exception as e:
            return False, f"数据库备份失败: {str(e)}"
    
    def vacuum_database(self):
        """压缩数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.execute("VACUUM")
            conn.close()
            return True, "数据库压缩成功"
        except Exception as e:
            return False, f"数据库压缩失败: {str(e)}"
    
    def execute_query(self, query, params=None):
        """执行SQL查询"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if query.strip().upper().startswith('SELECT'):
                results = cursor.fetchall()
                columns = [description[0] for description in cursor.description]
                conn.close()
                return True, {'results': results, 'columns': columns}
            else:
                conn.commit()
                affected_rows = cursor.rowcount
                conn.close()
                return True, {'affected_rows': affected_rows}
                
        except Exception as e:
            return False, str(e)
