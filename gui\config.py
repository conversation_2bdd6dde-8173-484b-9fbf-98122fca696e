"""
GUI配置文件
"""

import os
import json
from datetime import datetime

class GUIConfig:
    """GUI配置管理器"""
    
    def __init__(self):
        # 获取配置文件路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        self.config_file = os.path.join(current_dir, 'gui_config.json')
        
        # 默认配置
        self.default_config = {
            'window': {
                'width': 1400,
                'height': 900,
                'x': 100,
                'y': 100
            },
            'execution': {
                'max_workers': 4,
                'auto_refresh': True,
                'auto_check_db': True
            },
            'logging': {
                'max_lines': 1000,
                'auto_scroll': True,
                'show_timestamps': True
            },
            'database': {
                'path': 'data.db',
                'auto_backup': False,
                'backup_interval': 24  # 小时
            },
            'ui': {
                'theme': 'default',
                'font_size': 9,
                'show_tooltips': True
            }
        }
        
        # 加载配置
        self.config = self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 合并默认配置和用户配置
                merged_config = self.default_config.copy()
                self._deep_update(merged_config, config)
                return merged_config
            else:
                # 如果配置文件不存在，使用默认配置并保存
                self.save_config(self.default_config)
                return self.default_config.copy()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.default_config.copy()
    
    def save_config(self, config=None):
        """保存配置文件"""
        try:
            if config is None:
                config = self.config
            
            # 添加保存时间戳
            config['_saved_at'] = datetime.now().isoformat()
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key_path, default=None):
        """获取配置值，支持点号分隔的路径"""
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path, value):
        """设置配置值，支持点号分隔的路径"""
        keys = key_path.split('.')
        config = self.config
        
        # 导航到最后一级的父级
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # 设置值
        config[keys[-1]] = value
    
    def _deep_update(self, base_dict, update_dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def reset_to_default(self):
        """重置为默认配置"""
        self.config = self.default_config.copy()
        return self.save_config()
    
    def export_config(self, file_path):
        """导出配置到指定文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
    
    def import_config(self, file_path):
        """从指定文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 验证配置格式
            if self._validate_config(imported_config):
                self.config = imported_config
                return self.save_config()
            else:
                print("导入的配置格式无效")
                return False
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False
    
    def _validate_config(self, config):
        """验证配置格式"""
        try:
            # 检查必要的顶级键
            required_keys = ['window', 'execution', 'logging', 'database', 'ui']
            for key in required_keys:
                if key not in config:
                    return False
            
            # 可以添加更详细的验证逻辑
            return True
        except:
            return False

# 全局配置实例
gui_config = GUIConfig()

# 便捷函数
def get_config(key_path, default=None):
    """获取配置值"""
    return gui_config.get(key_path, default)

def set_config(key_path, value):
    """设置配置值"""
    gui_config.set(key_path, value)

def save_config():
    """保存配置"""
    return gui_config.save_config()
