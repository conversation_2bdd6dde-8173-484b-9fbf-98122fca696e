import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)



# 添加项目根目录到Python路径

from config import get_token
import tushare as ts
import pandas as pd
import sqlite3
import time
from datetime import datetime, timedelta
# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

class StockDatabase:
    def __init__(self, db_name='data.db'):
        """初始化数据库连接"""
        self.db_name = db_name
        self.conn = sqlite3.connect(db_name)
        print(f"数据库 {db_name} 连接成功")

    def create_table(self):
        """创建周/月线行情表"""
        create_table_sql = '''CREATE TABLE IF NOT EXISTS stk_weekly_monthly (
            ts_code TEXT,
            trade_date TEXT,
            freq TEXT,
            open REAL,
            high REAL,
            low REAL,
            close REAL,
            pre_close REAL,
            vol REAL,
            amount REAL,
            change REAL,
            pct_chg REAL,
            PRIMARY KEY (ts_code, trade_date, freq)
        )'''
        
        try:
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 stk_weekly_monthly 创建成功")
        except Exception as e:
            print(f"创建表格 stk_weekly_monthly 时出错: {e}")

    def insert_data(self, df):
        """将DataFrame数据插入到表格中"""
        try:
            # 将DataFrame写入SQLite数据库，使用upsert模式
            df.to_sql('stk_weekly_monthly', self.conn, if_exists='append', index=False)
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")
        except Exception as e:
            print(f"写入数据时出错: {e}")

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_stock_list():
    """获取股票列表"""
    try:
        df = pro.stock_basic(exchange='', list_status='L', 
                            fields='ts_code,symbol,name,area,industry,list_date')
        return df
    except Exception as e:
        print(f"获取股票列表时出错: {e}")
        return None

def fetch_weekly_monthly_data(ts_code, start_date, end_date, freq):
    """获取单个股票的周/月线数据"""
    try:
        df = pro.stk_weekly_monthly(ts_code=ts_code,
                                  start_date=start_date,
                                  end_date=end_date,
                                  freq=freq)
        # 添加延时以避免频率限制
        time.sleep(0.5)
        return df
    except Exception as e:
        print(f"获取 {ts_code} 的{freq}线数据时出错: {e}")
        return None

def main():
    # 确保market_data目录存在
    os.makedirs('market_data', exist_ok=True)
    
    # 创建数据库实例
    db = StockDatabase()
    db.create_table()
    
    try:
        # 获取股票列表
        stock_list = get_stock_list()
        if stock_list is None:
            return
        
        # 设置时间范围（近3年）
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=3*365)).strftime('%Y%m%d')
        
        # 设置频率列表
        freqs = ['week', 'month']
        
        total_stocks = len(stock_list)
        for idx, row in stock_list.iterrows():
            ts_code = row['ts_code']
            print(f"正在处理第 {idx+1}/{total_stocks} 只股票: {ts_code}")
            
            for freq in freqs:
                freq_name = '周' if freq == 'week' else '月'
                print(f"获取{freq_name}线数据...")
                
                try:
                    df = fetch_weekly_monthly_data(ts_code, start_date, end_date, freq)
                    if df is not None and not df.empty:
                        db.insert_data(df)
                        print(f"成功获取 {ts_code} 的{freq_name}线数据，共 {len(df)} 条记录")
                except Exception as e:
                    print(f"处理 {ts_code} 的{freq_name}线数据时出错: {e}")
                    continue
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行股票周/月线行情数据获取程序...")
    main()
    print("程序运行完成！") 