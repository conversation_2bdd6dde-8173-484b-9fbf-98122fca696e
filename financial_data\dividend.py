import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)



# 添加项目根目录到Python路径

from config import get_token
import tushare as ts
import pandas as pd
import sqlite3
import time
from datetime import datetime, timedelta
# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

class StockDatabase:
    def __init__(self, db_name='data.db'):
        """初始化数据库连接"""
        self.db_name = db_name
        self.conn = sqlite3.connect(db_name)
        print(f"数据库 {db_name} 连接成功")

    def create_table(self):
        """创建分红送股表"""
        try:
            # 构建创建表的SQL语句
            create_table_sql = '''CREATE TABLE IF NOT EXISTS dividend (
                ts_code TEXT,
                end_date TEXT,
                ann_date TEXT,
                div_proc TEXT,
                stk_div REAL,
                stk_bo_rate REAL,
                stk_co_rate REAL,
                cash_div REAL,
                cash_div_tax REAL,
                record_date TEXT,
                ex_date TEXT,
                pay_date TEXT,
                div_listdate TEXT,
                imp_ann_date TEXT,
                base_date TEXT,
                base_share REAL,
                PRIMARY KEY (ts_code, end_date, ann_date)
            )'''
            
            # 首先删除已存在的表
            self.conn.execute('DROP TABLE IF EXISTS dividend')
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 dividend 创建成功")
            
        except Exception as e:
            print(f"创建表格 dividend 时出错: {e}")
            raise

    def insert_data(self, df):
        """将DataFrame数据插入到表格中"""
        try:
            # 创建临时表
            temp_table_name = 'temp_dividend'
            df.to_sql(temp_table_name, self.conn, if_exists='replace', index=False)
            
            # 使用INSERT OR REPLACE将数据从临时表插入到主表
            insert_sql = f'''
                INSERT OR REPLACE INTO dividend 
                SELECT * FROM {temp_table_name}
            '''
            self.conn.execute(insert_sql)
            
            # 删除临时表
            self.conn.execute(f'DROP TABLE IF EXISTS {temp_table_name}')
            
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")
        except Exception as e:
            print(f"写入数据时出错: {e}")
            self.conn.rollback()

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_stock_list():
    """获取股票列表"""
    try:
        # 获取所有股票列表
        stocks = pro.stock_basic(exchange='', list_status='L')
        return stocks['ts_code'].tolist()
    except Exception as e:
        print(f"获取股票列表时出错: {e}")
        return []

def fetch_dividend_data(ts_code, start_date):
    """获取单个股票的分红送股数据"""
    try:
        print(f"正在获取 {ts_code} 的分红送股数据...")
        df = pro.dividend(ts_code=ts_code, start_date=start_date)
        time.sleep(0.5)  # 添加延时以避免频率限制
        return df
    except Exception as e:
        print(f"获取 {ts_code} 的分红送股数据时出错: {e}")
        return None

def get_start_date():
    """获取5年前的日期"""
    now = datetime.now()
    start_date = (now - timedelta(days=3*365)).strftime('%Y%m%d')
    return start_date

def process_stock_data(ts_code, start_date, db):
    """处理单个股票的数据"""
    try:
        df = fetch_dividend_data(ts_code, start_date)
        if df is not None and not df.empty:
            db.insert_data(df)
            print(f"成功获取 {ts_code} 的分红送股数据，共 {len(df)} 条记录")
            return True
        return False
    except Exception as e:
        print(f"处理 {ts_code} 的分红送股数据时出错: {e}")
        return False

def main():
    # 确保financial_data目录存在
    os.makedirs('financial_data', exist_ok=True)
    
    # 创建数据库实例
    db = StockDatabase()
    
    try:
        # 创建表
        db.create_table()
        
        # 获取开始日期
        start_date = get_start_date()
        print(f"将获取 {start_date} 之后的分红送股数据")
        
        # 获取股票列表
        stock_list = get_stock_list()
        total_stocks = len(stock_list)
        print(f"共有 {total_stocks} 只股票需要处理")
        
        # 处理每只股票的数据
        for idx, ts_code in enumerate(stock_list):
            print(f"正在处理第 {idx+1}/{total_stocks} 只股票: {ts_code}")
            
            if not process_stock_data(ts_code, start_date, db):
                print(f"跳过 {ts_code} 的处理")
                continue
            
            # 每处理50只股票后暂停一段时间
            if (idx + 1) % 50 == 0:
                print(f"已处理 {idx+1} 只股票，暂停1分钟...")
                time.sleep(60)
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行A股分红送股数据获取程序...")
    main()
    print("程序运行完成！") 