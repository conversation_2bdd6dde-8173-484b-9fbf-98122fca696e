simplejson-3.20.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
simplejson-3.20.1.dist-info/LICENSE.txt,sha256=lgGyr2JNUwdKNxIpgV46hTcDIEHWBf38Wljfb8OhuAI,10454
simplejson-3.20.1.dist-info/METADATA,sha256=BS4EhfCBC8zRhwce5tf3gf5oZNKmNZtM8dNnb6q-uVw,3407
simplejson-3.20.1.dist-info/RECORD,,
simplejson-3.20.1.dist-info/WHEEL,sha256=cRmSBGD-cl98KkuHMNqv9Ac9L9_VqTvcBYwpIvxN0cg,101
simplejson-3.20.1.dist-info/top_level.txt,sha256=ohU816e0ltfT8aBFol4_XY1q9GBh9tEBbcPpNn49200,11
simplejson/__init__.py,sha256=ENcrUzM1NCcHILn7pe3KSsj21Oh3XfoEKb16G2xhuzY,24059
simplejson/__pycache__/__init__.cpython-312.pyc,,
simplejson/__pycache__/compat.cpython-312.pyc,,
simplejson/__pycache__/decoder.cpython-312.pyc,,
simplejson/__pycache__/encoder.cpython-312.pyc,,
simplejson/__pycache__/errors.cpython-312.pyc,,
simplejson/__pycache__/ordered_dict.cpython-312.pyc,,
simplejson/__pycache__/raw_json.cpython-312.pyc,,
simplejson/__pycache__/scanner.cpython-312.pyc,,
simplejson/__pycache__/tool.cpython-312.pyc,,
simplejson/_speedups.cp312-win_amd64.pyd,sha256=G5b6aq05P8AgjWONU15I40A0ZJl2AknrbBtxQCpf9BY,40960
simplejson/compat.py,sha256=dtHjGYyJ7YIPHo6TAdPo_xSGKAwzbeCP9LxIKPlxniU,849
simplejson/decoder.py,sha256=OuXHknGC2XPY_wROEfAUL9DqhYnGoqKszvvUdlm82Ro,15549
simplejson/encoder.py,sha256=E0wKuhuvYD8xWKg6dmhaSyxjG5x-kpqtUdosaVZjSTo,29833
simplejson/errors.py,sha256=kh_PdrhiVGhNvvLuVocjEvchVX-Pn7ybzRPtkAqhk6g,1832
simplejson/ordered_dict.py,sha256=JLXyqPz2ruSaVONd14d5AR02ZHMytCn-RoZ2UjkpJBQ,3048
simplejson/raw_json.py,sha256=uyJbc3jCrLIBIB8vbNUwLCZlys17gZsyjNGwymwMJ8o,226
simplejson/scanner.py,sha256=8bnHgkMjlY9EnJaZEzbw2K6l_7Ld_bX-Xt2O2gJTTkw,3113
simplejson/tests/__init__.py,sha256=FEOzE1-8wKj1sgz7daLpJtOzkxMMf2BlBIuaxuntbhI,2603
simplejson/tests/__pycache__/__init__.cpython-312.pyc,,
simplejson/tests/__pycache__/_cibw_runner.cpython-312.pyc,,
simplejson/tests/__pycache__/test_bigint_as_string.cpython-312.pyc,,
simplejson/tests/__pycache__/test_bitsize_int_as_string.cpython-312.pyc,,
simplejson/tests/__pycache__/test_check_circular.cpython-312.pyc,,
simplejson/tests/__pycache__/test_decimal.cpython-312.pyc,,
simplejson/tests/__pycache__/test_decode.cpython-312.pyc,,
simplejson/tests/__pycache__/test_default.cpython-312.pyc,,
simplejson/tests/__pycache__/test_dump.cpython-312.pyc,,
simplejson/tests/__pycache__/test_encode_basestring_ascii.cpython-312.pyc,,
simplejson/tests/__pycache__/test_encode_for_html.cpython-312.pyc,,
simplejson/tests/__pycache__/test_errors.cpython-312.pyc,,
simplejson/tests/__pycache__/test_fail.cpython-312.pyc,,
simplejson/tests/__pycache__/test_float.cpython-312.pyc,,
simplejson/tests/__pycache__/test_for_json.cpython-312.pyc,,
simplejson/tests/__pycache__/test_indent.cpython-312.pyc,,
simplejson/tests/__pycache__/test_item_sort_key.cpython-312.pyc,,
simplejson/tests/__pycache__/test_iterable.cpython-312.pyc,,
simplejson/tests/__pycache__/test_namedtuple.cpython-312.pyc,,
simplejson/tests/__pycache__/test_pass1.cpython-312.pyc,,
simplejson/tests/__pycache__/test_pass2.cpython-312.pyc,,
simplejson/tests/__pycache__/test_pass3.cpython-312.pyc,,
simplejson/tests/__pycache__/test_raw_json.cpython-312.pyc,,
simplejson/tests/__pycache__/test_recursion.cpython-312.pyc,,
simplejson/tests/__pycache__/test_scanstring.cpython-312.pyc,,
simplejson/tests/__pycache__/test_separators.cpython-312.pyc,,
simplejson/tests/__pycache__/test_speedups.cpython-312.pyc,,
simplejson/tests/__pycache__/test_str_subclass.cpython-312.pyc,,
simplejson/tests/__pycache__/test_subclass.cpython-312.pyc,,
simplejson/tests/__pycache__/test_tool.cpython-312.pyc,,
simplejson/tests/__pycache__/test_tuple.cpython-312.pyc,,
simplejson/tests/__pycache__/test_unicode.cpython-312.pyc,,
simplejson/tests/_cibw_runner.py,sha256=QYFxRnOUEQ-3zr6SOuU01MrutoRtjw1lVdztj8vT3XA,180
simplejson/tests/test_bigint_as_string.py,sha256=K2WKYEho5GFWZra1hyspKu7vJspvH22NkBr7_A2Q59A,2305
simplejson/tests/test_bitsize_int_as_string.py,sha256=NKVrxVXGGLWwqPwP_qU_-5XlTGwcmMd-WxYa_X9_c2I,2370
simplejson/tests/test_check_circular.py,sha256=_UOPNwAXTyJZ-FUZja5HBkk1RNgWlmy_-XA0LBj70CI,947
simplejson/tests/test_decimal.py,sha256=xSupMjTxhhWYhOl6i_Pd2FlSwIZK_kEt_U170Gc3fdM,2615
simplejson/tests/test_decode.py,sha256=H5qOqpVYooIf8nnSyv3MqMYpyacZYcJ456zYn-uFfU8,5308
simplejson/tests/test_default.py,sha256=eRMug83jI4g6iS9HWhccWfpMWaPBbEZ8CR5YcqxC1U0,230
simplejson/tests/test_dump.py,sha256=cruioEq452TmNPnd1vFjK0ndletrOj9s_BZy-m6rKUY,10825
simplejson/tests/test_encode_basestring_ascii.py,sha256=mAQoabolxwHbkJLvBUma_j2Ni_72sfLzvPnm5Wx9sJk,2384
simplejson/tests/test_encode_for_html.py,sha256=5pxnlYjM-vJcXDYMISzUo5IfMBBnNNTbGzJcM2Zfc6U,1553
simplejson/tests/test_errors.py,sha256=YJS0y1HnZxpXdkqeON-9YMvIIrViS46d7ODhD9VOwY0,2149
simplejson/tests/test_fail.py,sha256=sFa8xG9HoJm1DDKnA6nn20c--_jA7tozsxPDPVxsM5E,6632
simplejson/tests/test_float.py,sha256=jEen2h6TmKsYbVYIchzY4Oxl20lhUxusJEL5IIJpxpM,1714
simplejson/tests/test_for_json.py,sha256=VRyoNzs-zgWz0KWa-OiXPj0L6NFBJc5YZFu9gh-GYLc,2864
simplejson/tests/test_indent.py,sha256=d8bgf-kctejI7rxIUJ5z5ca6eC1tlxc2jw3l3yyGx8o,2654
simplejson/tests/test_item_sort_key.py,sha256=D6vmBcQrwtBAqM1HJ0KJ-3-uMKyO7TbDbFJQm_bdQWk,1403
simplejson/tests/test_iterable.py,sha256=j6ZXdwqldJJj-G5fXsrTkU8lFeKjHCB3J5AOhnSxJxI,1421
simplejson/tests/test_namedtuple.py,sha256=qe8pSepW_W0ri8arGiyZulh_zfOP0KWjn_CYMTOX3oE,6070
simplejson/tests/test_pass1.py,sha256=R67t7gwXXGBaajINHSWuzumGVbanW92lxcYaXd_1P_o,1817
simplejson/tests/test_pass2.py,sha256=kUh5-Jga5s7hbH_CtbuNbJjw-fgl6HliJHV3ll3HfcY,400
simplejson/tests/test_pass3.py,sha256=dDBXatB6LnaGKBoylYvG7oV6meAypylRtUIkqbiKn5k,502
simplejson/tests/test_raw_json.py,sha256=2auryOgQ-aLZGU83FqMGvT-ZMIh16yaNgs8kEJ-puFc,1109
simplejson/tests/test_recursion.py,sha256=RvfIsX-LsCkk0HCJ3NP6aP-4AK0E7nj-CBLjaYUg6ns,1746
simplejson/tests/test_scanstring.py,sha256=qYlVmTWHc3lzbWxBEtUWkOtEhkq39S_c-fNWSSiZSg8,7848
simplejson/tests/test_separators.py,sha256=Sy7MO9q4_4q_GiJz7eeev4KadIj2GHqnRCKtMGl_N0A,984
simplejson/tests/test_speedups.py,sha256=CJhpPSKhYpZjd9D-iBYj7sIPXCEklkuyc2A8uKjsTdo,4258
simplejson/tests/test_str_subclass.py,sha256=Ho-hQd-aOAQg17f48y53G2aAU_CKl8wgJx6ubWzIjO4,761
simplejson/tests/test_subclass.py,sha256=eaX4SJkMcOOMVxUaT3LBMDDRgvNpSd4MtsPNq4eJGVc,1161
simplejson/tests/test_tool.py,sha256=OHlQtEPpCh4v7s5HAuBlixeQ4JCWC5VC77QxMSZ_SXc,3418
simplejson/tests/test_tuple.py,sha256=rBUzmeTepn8kdW5gw_0ypLvwWENuHPgL5OXjJXf5ryQ,1878
simplejson/tests/test_unicode.py,sha256=aiMVgn9uUSYx6lbpZspGQxyDOoBEmVFdqTWacO15ewE,7210
simplejson/tool.py,sha256=1UznEW0s-UuJX_GIcb_rnMX1DK_Az0As1bJOliwsNnw,1178
