#!/usr/bin/env python
"""
股票市场新闻采集工具启动脚本
"""

import os
import sys
import subprocess
import sqlite3

def check_dependencies():
    """检查依赖库是否已安装"""
    try:
        import requests
        import bs4
        try:
            # 检查DrissionPage依赖
            from DrissionPage import ChromiumPage
            print("所有依赖库已安装")
            return True
        except ImportError:
            print("缺少DrissionPage库")
            return False
    except ImportError:
        print("缺少基本依赖库")
        return False

def install_dependencies():
    """安装依赖库"""
    try:
        print("安装依赖库...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        
        # 检查DrissionPage是否已正确安装
        try:
            from DrissionPage import ChromiumPage
            print("DrissionPage已成功安装")
            return True
        except ImportError:
            print("DrissionPage安装失败，尝试单独安装")
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'DrissionPage>=4.0.0'])
                from DrissionPage import ChromiumPage
                print("DrissionPage已成功安装")
                return True
            except Exception as e:
                print(f"DrissionPage安装失败: {e}")
                return False
    except subprocess.CalledProcessError as e:
        print(f"安装依赖库失败: {e}")
        return False

def check_database():
    """检查数据库是否存在并可访问"""
    try:
        # 获取项目根目录
        root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        db_path = os.path.join(root_dir, 'data.db')
        
        # 测试连接
        conn = sqlite3.connect(db_path)
        
        # 检查news表是否存在
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='news'")
        if not cursor.fetchone():
            # 如果表不存在，初始化表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS news (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    date TEXT,
                    source TEXT,
                    url TEXT,
                    content TEXT,
                    fetch_time TEXT NOT NULL,
                    UNIQUE(title, source, date)
                )
            ''')
            conn.commit()
            print(f"已创建数据库表: {db_path}")
        else:
            print(f"数据库检查完成: {db_path}")
        
        conn.close()
        return True
    except sqlite3.Error as e:
        print(f"数据库检查出错: {e}")
        return False

def check_browser():
    """检查Chrome浏览器是否已安装"""
    try:
        # DrissionPage 4.0+版本使用不同的模块路径
        try:
            # 先尝试新版本的导入方式
            from DrissionPage.configs.chromium_options import ChromiumOptions
            options = ChromiumOptions()
            print("使用DrissionPage 4.0+版本")
        except ImportError:
            # 尝试旧版本的导入方式
            from DrissionPage import ChromiumOptions
            options = ChromiumOptions()
            print("使用DrissionPage旧版本")
        
        # 简单检查Chrome是否可以被启动
        print("检查Chrome浏览器...")
        
        # 不再直接获取Chrome路径，而是假设Chrome可用
        # 如果安装了DrissionPage，一般来说已经包含了Chrome支持
        print("DrissionPage已安装，假定Chrome浏览器可用")
        return True
    except Exception as e:
        print(f"检查Chrome浏览器时出错: {e}")
        # 打印更详细的错误信息
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """主函数"""
    # 检查当前目录是否是NEWS目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if os.path.basename(current_dir) != 'NEWS':
        print("请在NEWS目录下运行此脚本")
        return
    
    # 检查依赖
    if not check_dependencies():
        print("正在安装依赖库...")
        if not install_dependencies():
            print("安装依赖库失败，请手动运行: pip install -r requirements.txt")
            return
    
    # 检查Chrome浏览器
    if not check_browser():
        return
    
    # 检查数据库
    if not check_database():
        print("数据库初始化失败，请检查数据库文件权限或SQLite安装")
        return
    
    # 运行主程序
    try:
        print("启动股票市场新闻采集工具...")
        from stock_news_crawler import main
        main()
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    main() 