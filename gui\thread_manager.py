import threading
import concurrent.futures
import importlib
import sys
import os
import traceback
import time
from datetime import datetime

class ThreadManager:
    """线程管理器，负责并行执行脚本"""
    
    def __init__(self):
        self.executor = None
        self.futures = {}
        self.is_stopping = False
        self.running = False
        
    def is_running(self):
        """检查是否有任务在运行"""
        return self.running
    
    def start_execution(self, scripts, max_workers, message_queue):
        """开始执行脚本列表"""
        if self.running:
            return False, "已有任务在运行"
        
        self.running = True
        self.is_stopping = False
        
        # 在新线程中启动执行器
        execution_thread = threading.Thread(
            target=self._execute_scripts,
            args=(scripts, max_workers, message_queue),
            daemon=True
        )
        execution_thread.start()
        
        return True, "任务已启动"
    
    def _execute_scripts(self, scripts, max_workers, message_queue):
        """在后台线程中执行脚本"""
        completed = 0
        failed = 0
        
        try:
            # 创建线程池
            self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
            
            # 提交所有任务
            future_to_script = {}
            for script in scripts:
                if self.is_stopping:
                    break
                    
                future = self.executor.submit(self._run_single_script, script, message_queue)
                future_to_script[future] = script
                self.futures[script['item_id']] = future
            
            # 等待任务完成
            for future in concurrent.futures.as_completed(future_to_script):
                if self.is_stopping:
                    break
                    
                script = future_to_script[future]
                completed += 1
                
                try:
                    success, message = future.result()
                    if not success:
                        failed += 1
                        
                    # 发送完成消息
                    message_queue.put({
                        'type': 'script_complete',
                        'item_id': script['item_id'],
                        'script_name': script['name'],
                        'success': success,
                        'message': message
                    })
                    
                except Exception as e:
                    failed += 1
                    message_queue.put({
                        'type': 'script_complete',
                        'item_id': script['item_id'],
                        'script_name': script['name'],
                        'success': False,
                        'message': f"执行异常: {str(e)}"
                    })
                
                # 发送进度更新
                message_queue.put({
                    'type': 'progress_update',
                    'completed': completed,
                    'total': len(scripts)
                })
            
        except Exception as e:
            message_queue.put({
                'type': 'log',
                'level': 'ERROR',
                'message': f"执行过程中发生错误: {str(e)}"
            })
        
        finally:
            # 清理
            if self.executor:
                self.executor.shutdown(wait=True)
                self.executor = None
            
            self.futures.clear()
            self.running = False
            
            # 发送完成消息
            message_queue.put({
                'type': 'all_complete',
                'completed': completed,
                'total': len(scripts),
                'failed': failed
            })
    
    def _run_single_script(self, script, message_queue):
        """运行单个脚本"""
        script_path = script['path']
        script_name = script['name']
        
        try:
            # 发送开始消息
            message_queue.put({
                'type': 'script_start',
                'item_id': script['item_id'],
                'script_name': script_name
            })
            
            # 记录开始时间
            start_time = time.time()
            
            # 动态导入并执行模块
            success, message = self._import_and_run_module(script_path, message_queue)
            
            # 记录结束时间
            end_time = time.time()
            duration = round(end_time - start_time, 2)
            
            if success:
                final_message = f"{message} (耗时: {duration}秒)"
            else:
                final_message = f"{message} (耗时: {duration}秒)"
            
            return success, final_message
            
        except Exception as e:
            error_msg = f"运行脚本时发生异常: {str(e)}"
            message_queue.put({
                'type': 'log',
                'level': 'ERROR',
                'message': f"{script_name}: {error_msg}"
            })
            return False, error_msg
    
    def _import_and_run_module(self, script_path, message_queue):
        """导入并运行模块"""
        try:
            # 获取模块路径信息
            script_dir = os.path.dirname(script_path)
            script_filename = os.path.basename(script_path)
            module_name = script_filename[:-3]  # 去掉.py后缀
            
            # 获取相对于项目根目录的模块路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            root_dir = os.path.dirname(current_dir)
            
            # 计算相对路径
            rel_path = os.path.relpath(script_dir, root_dir)
            if rel_path == '.':
                full_module_name = module_name
            else:
                full_module_name = rel_path.replace(os.sep, '.') + '.' + module_name
            
            # 确保项目根目录在Python路径中
            if root_dir not in sys.path:
                sys.path.insert(0, root_dir)
            
            # 动态导入模块
            try:
                # 如果模块已经导入过，重新加载
                if full_module_name in sys.modules:
                    module = importlib.reload(sys.modules[full_module_name])
                else:
                    module = importlib.import_module(full_module_name)
            except ImportError as e:
                # 如果导入失败，尝试直接从文件路径导入
                import importlib.util
                spec = importlib.util.spec_from_file_location(module_name, script_path)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
            
            # 检查是否有main函数
            if not hasattr(module, 'main'):
                return False, "模块中没有找到main函数"
            
            # 执行main函数
            try:
                result = module.main()
                
                # 如果main函数返回了结果，处理返回值
                if result is not None:
                    if isinstance(result, dict):
                        success = result.get('success', True)
                        message = result.get('message', '执行完成')
                        return success, message
                    elif isinstance(result, bool):
                        return result, "执行完成" if result else "执行失败"
                
                return True, "执行完成"
                
            except Exception as e:
                error_msg = f"执行main函数时出错: {str(e)}"
                message_queue.put({
                    'type': 'log',
                    'level': 'ERROR',
                    'message': f"详细错误: {traceback.format_exc()}"
                })
                return False, error_msg
                
        except Exception as e:
            error_msg = f"导入模块时出错: {str(e)}"
            message_queue.put({
                'type': 'log',
                'level': 'ERROR',
                'message': f"详细错误: {traceback.format_exc()}"
            })
            return False, error_msg
    
    def stop_all(self):
        """停止所有任务"""
        self.is_stopping = True
        
        if self.executor:
            # 取消所有未开始的任务
            for future in self.futures.values():
                future.cancel()
            
            # 关闭执行器
            self.executor.shutdown(wait=False)
            
        # 清理状态
        self.futures.clear()
        self.running = False
    
    def get_running_tasks(self):
        """获取正在运行的任务信息"""
        if not self.executor:
            return []
        
        running_tasks = []
        for item_id, future in self.futures.items():
            if not future.done():
                running_tasks.append({
                    'item_id': item_id,
                    'status': 'running' if future.running() else 'pending'
                })
        
        return running_tasks
