import os
import sqlite3
import json
from datetime import datetime

class NewsDatabase:
    """新闻数据库管理类"""
    
    def __init__(self, db_path=None):
        """初始化数据库连接"""
        if db_path is None:
            # 使用项目根目录下的data.db文件
            root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            self.db_path = os.path.join(root_dir, 'data.db')
        else:
            self.db_path = db_path
            
        self.conn = None
        self.cursor = None
    
    def connect(self):
        """连接到数据库"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.cursor = self.conn.cursor()
            self._create_tables()
            return True
        except sqlite3.Error as e:
            print(f"数据库连接错误: {e}")
            return False
    
    def _create_tables(self):
        """创建必要的表结构"""
        try:
            # 创建新闻表
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS news (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    date TEXT,
                    source TEXT,
                    url TEXT,
                    content TEXT,
                    fetch_time TEXT NOT NULL,
                    UNIQUE(title, source, date)
                )
            ''')
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"创建表失败: {e}")
            return False
    
    def save_news(self, news_items):
        """保存新闻项到数据库"""
        if not self.connect():
            return False, "无法连接到数据库"
        
        try:
            fetch_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            inserted_count = 0
            updated_count = 0
            
            for news in news_items:
                # 检查是否存在相同新闻
                self.cursor.execute('''
                    SELECT id, content FROM news 
                    WHERE title = ? AND source = ? AND (date = ? OR date IS NULL)
                ''', (news['title'], news['source'], news.get('date', '')))
                
                existing = self.cursor.fetchone()
                
                if existing:
                    # 如果存在且内容为空但现在有内容，则更新
                    news_id, existing_content = existing
                    if not existing_content and news.get('content'):
                        self.cursor.execute('''
                            UPDATE news 
                            SET content = ?, fetch_time = ? 
                            WHERE id = ?
                        ''', (news.get('content', ''), fetch_time, news_id))
                        updated_count += 1
                else:
                    # 插入新记录
                    self.cursor.execute('''
                        INSERT INTO news (title, date, source, url, content, fetch_time)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        news['title'],
                        news.get('date', ''),
                        news.get('source', ''),
                        news.get('url', ''),
                        news.get('content', ''),
                        fetch_time
                    ))
                    inserted_count += 1
            
            self.conn.commit()
            return True, f"成功保存 {inserted_count} 条新闻，更新 {updated_count} 条新闻"
        
        except sqlite3.Error as e:
            self.conn.rollback()
            return False, f"保存新闻失败: {e}"
        finally:
            self.close()
    
    def get_news(self, limit=100, source=None):
        """获取新闻列表"""
        if not self.connect():
            return []
        
        try:
            if source:
                self.cursor.execute('''
                    SELECT id, title, date, source, url, content, fetch_time
                    FROM news 
                    WHERE source = ?
                    ORDER BY fetch_time DESC
                    LIMIT ?
                ''', (source, limit))
            else:
                self.cursor.execute('''
                    SELECT id, title, date, source, url, content, fetch_time
                    FROM news 
                    ORDER BY fetch_time DESC
                    LIMIT ?
                ''', (limit,))
            
            news_items = []
            for row in self.cursor.fetchall():
                news_items.append({
                    'id': row[0],
                    'title': row[1],
                    'date': row[2],
                    'source': row[3],
                    'url': row[4],
                    'content': row[5],
                    'fetch_time': row[6]
                })
            
            return news_items
        
        except sqlite3.Error as e:
            print(f"获取新闻失败: {e}")
            return []
        finally:
            self.close()
    
    def search_news(self, keyword, limit=100):
        """搜索新闻"""
        if not self.connect():
            return []
        
        try:
            # 构建搜索条件
            search_param = f"%{keyword}%"
            
            self.cursor.execute('''
                SELECT id, title, date, source, url, content, fetch_time
                FROM news 
                WHERE title LIKE ? OR content LIKE ?
                ORDER BY fetch_time DESC
                LIMIT ?
            ''', (search_param, search_param, limit))
            
            news_items = []
            for row in self.cursor.fetchall():
                news_items.append({
                    'id': row[0],
                    'title': row[1],
                    'date': row[2],
                    'source': row[3],
                    'url': row[4],
                    'content': row[5],
                    'fetch_time': row[6]
                })
            
            return news_items
        
        except sqlite3.Error as e:
            print(f"搜索新闻失败: {e}")
            return []
        finally:
            self.close()
    
    def delete_news(self, news_id):
        """删除指定新闻"""
        if not self.connect():
            return False, "无法连接到数据库"
        
        try:
            self.cursor.execute('DELETE FROM news WHERE id = ?', (news_id,))
            self.conn.commit()
            
            if self.cursor.rowcount > 0:
                return True, "删除成功"
            else:
                return False, "未找到指定新闻"
        
        except sqlite3.Error as e:
            self.conn.rollback()
            return False, f"删除失败: {e}"
        finally:
            self.close()
    
    def close(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        
        self.cursor = None
        self.conn = None 