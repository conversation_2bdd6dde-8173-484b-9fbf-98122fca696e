import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)



# 添加项目根目录到Python路径

from config import get_token
import tushare as ts
import pandas as pd
import sqlite3
import time
from datetime import datetime, timedelta
# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

class StockDatabase:
    def __init__(self, db_name='data.db'):
        """初始化数据库连接"""
        self.db_name = db_name
        self.conn = sqlite3.connect(db_name)
        print(f"数据库 {db_name} 连接成功")

    def create_table(self):
        """创建停复牌信息表"""
        # 首先删除已存在的表
        drop_table_sql = 'DROP TABLE IF EXISTS suspend_d'
        create_table_sql = '''CREATE TABLE IF NOT EXISTS suspend_d (
            ts_code TEXT,
            trade_date TEXT,
            suspend_timing TEXT,
            suspend_type TEXT,
            PRIMARY KEY (ts_code, trade_date, suspend_type)
        )'''
        
        try:
            self.conn.execute(drop_table_sql)
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 suspend_d 创建成功")
        except Exception as e:
            print(f"创建表格 suspend_d 时出错: {e}")

    def insert_data(self, df):
        """将DataFrame数据插入到表格中"""
        try:
            # 将DataFrame写入SQLite数据库
            df.to_sql('suspend_d', self.conn, if_exists='append', index=False)
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")
        except Exception as e:
            print(f"写入数据时出错: {e}")

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_trade_calendar(start_date, end_date):
    """获取交易日历"""
    try:
        df = pro.trade_cal(exchange='SSE', 
                          start_date=start_date,
                          end_date=end_date,
                          is_open='1')
        return df['cal_date'].tolist()
    except Exception as e:
        print(f"获取交易日历时出错: {e}")
        return None

def get_date_range():
    """获取近5年的日期范围"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3*365)
    return start_date.strftime('%Y%m%d'), end_date.strftime('%Y%m%d')

def fetch_suspend_data(trade_date, suspend_type=None):
    """获取单个交易日的停复牌信息"""
    try:
        print(f"正在获取 {trade_date} 的停复牌信息...")
        df = pro.suspend_d(trade_date=trade_date, suspend_type=suspend_type)
        time.sleep(0.5)  # 添加延时以避免频率限制
        return df
    except Exception as e:
        print(f"获取 {trade_date} 的停复牌信息时出错: {e}")
        return None

def process_daily_data(trade_date, db):
    """处理单个交易日的数据"""
    try:
        # 分别获取停牌和复牌信息
        suspend_types = ['S', 'R']
        all_data = []
        
        for suspend_type in suspend_types:
            df = fetch_suspend_data(trade_date, suspend_type)
            if df is not None and not df.empty:
                all_data.append(df)
        
        if all_data:
            # 合并所有数据
            combined_df = pd.concat(all_data, ignore_index=True)
            db.insert_data(combined_df)
            print(f"成功获取 {trade_date} 的停复牌信息，共 {len(combined_df)} 条记录")
            return True
        return False
    except Exception as e:
        print(f"处理 {trade_date} 的停复牌信息时出错: {e}")
        return False

def main():
    # 确保market_data目录存在
    os.makedirs('market_data', exist_ok=True)
    
    # 创建数据库实例
    db = StockDatabase()
    db.create_table()
    
    try:
        # 获取日期范围
        start_date, end_date = get_date_range()
        print(f"获取数据的日期范围: {start_date} 至 {end_date}")
        
        # 获取交易日历
        trade_dates = get_trade_calendar(start_date, end_date)
        if trade_dates is None:
            return
        
        total_dates = len(trade_dates)
        for idx, trade_date in enumerate(trade_dates):
            print(f"正在处理第 {idx+1}/{total_dates} 个交易日: {trade_date}")
            
            if not process_daily_data(trade_date, db):
                print(f"跳过 {trade_date} 的处理")
                continue
            
            # 每处理20个交易日后额外等待一段时间
            if (idx + 1) % 20 == 0:
                print(f"已处理 {idx+1} 个交易日，暂停30秒...")
                time.sleep(30)
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行A股停复牌信息获取程序...")
    main()
    print("程序运行完成！") 