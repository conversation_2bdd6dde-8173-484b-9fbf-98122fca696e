import os
import sys
import time
import pandas as pd
import sqlite3
import tushare as ts
from datetime import datetime, timedelta
from threading import Lock
from collections import deque

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.insert(0, root_dir)

from config import get_token

# 设置token
ts.set_token(get_token())
pro = ts.pro_api()

# 频率控制相关变量
REQUEST_LIMIT = 500  # 每分钟请求限制（基础积分用户）
WINDOW_SIZE = 60    # 时间窗口大小（秒）
request_times = deque(maxlen=REQUEST_LIMIT)  # 存储请求时间，限制队列大小
request_lock = Lock()   # 线程锁，用于同步访问

class StockDatabase:
    def __init__(self, db_path=None, max_retries=3):
        """初始化数据库连接"""
        if db_path is None:
            # 使用项目根目录下的data.db
            db_path = os.path.join(root_dir, 'data.db')
        self.db_name = db_path
        self.conn = None
        
        # 尝试连接数据库
        for retry in range(max_retries):
            try:
                self.conn = sqlite3.connect(db_path)
                print(f"数据库 {db_path} 连接成功")
                break
            except sqlite3.Error as e:
                if retry < max_retries - 1:
                    print(f"数据库连接失败: {e}，将在3秒后重试（第{retry + 1}次）...")
                    time.sleep(3)
                else:
                    print(f"数据库连接失败: {e}")
                    raise
        
        if self.conn is None:
            raise sqlite3.Error("无法连接到数据库")

    def create_table(self):
        """创建股票技术因子专业版表"""
        try:
            # 由于字段太多，我们将SQL语句分成多个部分
            create_table_sql = '''CREATE TABLE IF NOT EXISTS stk_factor_pro (
                ts_code TEXT,
                trade_date TEXT,
                open FLOAT,
                open_hfq FLOAT,
                open_qfq FLOAT,
                high FLOAT,
                high_hfq FLOAT,
                high_qfq FLOAT,
                low FLOAT,
                low_hfq FLOAT,
                low_qfq FLOAT,
                close FLOAT,
                close_hfq FLOAT,
                close_qfq FLOAT,
                pre_close FLOAT,
                change FLOAT,
                pct_chg FLOAT,
                vol FLOAT,
                amount FLOAT,
                turnover_rate FLOAT,
                turnover_rate_f FLOAT,
                volume_ratio FLOAT,
                pe FLOAT,
                pe_ttm FLOAT,
                pb FLOAT,
                ps FLOAT,
                ps_ttm FLOAT,
                dv_ratio FLOAT,
                dv_ttm FLOAT,
                total_share FLOAT,
                float_share FLOAT,
                free_share FLOAT,
                total_mv FLOAT,
                circ_mv FLOAT,
                adj_factor FLOAT,
                asi_bfq FLOAT,
                asi_hfq FLOAT,
                asi_qfq FLOAT,
                asit_bfq FLOAT,
                asit_hfq FLOAT,
                asit_qfq FLOAT,
                atr_bfq FLOAT,
                atr_hfq FLOAT,
                atr_qfq FLOAT,
                bbi_bfq FLOAT,
                bbi_hfq FLOAT,
                bbi_qfq FLOAT,
                bias1_bfq FLOAT,
                bias1_hfq FLOAT,
                bias1_qfq FLOAT,
                bias2_bfq FLOAT,
                bias2_hfq FLOAT,
                bias2_qfq FLOAT,
                bias3_bfq FLOAT,
                bias3_hfq FLOAT,
                bias3_qfq FLOAT,
                boll_lower_bfq FLOAT,
                boll_lower_hfq FLOAT,
                boll_lower_qfq FLOAT,
                boll_mid_bfq FLOAT,
                boll_mid_hfq FLOAT,
                boll_mid_qfq FLOAT,
                boll_upper_bfq FLOAT,
                boll_upper_hfq FLOAT,
                boll_upper_qfq FLOAT,
                brar_ar_bfq FLOAT,
                brar_ar_hfq FLOAT,
                brar_ar_qfq FLOAT,
                brar_br_bfq FLOAT,
                brar_br_hfq FLOAT,
                brar_br_qfq FLOAT,
                cci_bfq FLOAT,
                cci_hfq FLOAT,
                cci_qfq FLOAT,
                cr_bfq FLOAT,
                cr_hfq FLOAT,
                cr_qfq FLOAT,
                dfma_dif_bfq FLOAT,
                dfma_dif_hfq FLOAT,
                dfma_dif_qfq FLOAT,
                dfma_difma_bfq FLOAT,
                dfma_difma_hfq FLOAT,
                dfma_difma_qfq FLOAT,
                dmi_adx_bfq FLOAT,
                dmi_adx_hfq FLOAT,
                dmi_adx_qfq FLOAT,
                dmi_adxr_bfq FLOAT,
                dmi_adxr_hfq FLOAT,
                dmi_adxr_qfq FLOAT,
                dmi_mdi_bfq FLOAT,
                dmi_mdi_hfq FLOAT,
                dmi_mdi_qfq FLOAT,
                dmi_pdi_bfq FLOAT,
                dmi_pdi_hfq FLOAT,
                dmi_pdi_qfq FLOAT,
                downdays FLOAT,
                updays FLOAT,
                dpo_bfq FLOAT,
                dpo_hfq FLOAT,
                dpo_qfq FLOAT,
                madpo_bfq FLOAT,
                madpo_hfq FLOAT,
                madpo_qfq FLOAT,
                ema_bfq_10 FLOAT,
                ema_bfq_20 FLOAT,
                ema_bfq_250 FLOAT,
                ema_bfq_30 FLOAT,
                ema_bfq_5 FLOAT,
                ema_bfq_60 FLOAT,
                ema_bfq_90 FLOAT,
                ema_hfq_10 FLOAT,
                ema_hfq_20 FLOAT,
                ema_hfq_250 FLOAT,
                ema_hfq_30 FLOAT,
                ema_hfq_5 FLOAT,
                ema_hfq_60 FLOAT,
                ema_hfq_90 FLOAT,
                ema_qfq_10 FLOAT,
                ema_qfq_20 FLOAT,
                ema_qfq_250 FLOAT,
                ema_qfq_30 FLOAT,
                ema_qfq_5 FLOAT,
                ema_qfq_60 FLOAT,
                ema_qfq_90 FLOAT,
                emv_bfq FLOAT,
                emv_hfq FLOAT,
                emv_qfq FLOAT,
                maemv_bfq FLOAT,
                maemv_hfq FLOAT,
                maemv_qfq FLOAT,
                expma_12_bfq FLOAT,
                expma_12_hfq FLOAT,
                expma_12_qfq FLOAT,
                expma_50_bfq FLOAT,
                expma_50_hfq FLOAT,
                expma_50_qfq FLOAT,
                kdj_bfq FLOAT,
                kdj_hfq FLOAT,
                kdj_qfq FLOAT,
                kdj_d_bfq FLOAT,
                kdj_d_hfq FLOAT,
                kdj_d_qfq FLOAT,
                kdj_k_bfq FLOAT,
                kdj_k_hfq FLOAT,
                kdj_k_qfq FLOAT,
                ktn_down_bfq FLOAT,
                ktn_down_hfq FLOAT,
                ktn_down_qfq FLOAT,
                ktn_mid_bfq FLOAT,
                ktn_mid_hfq FLOAT,
                ktn_mid_qfq FLOAT,
                ktn_upper_bfq FLOAT,
                ktn_upper_hfq FLOAT,
                ktn_upper_qfq FLOAT,
                lowdays FLOAT,
                topdays FLOAT,
                ma_bfq_10 FLOAT,
                ma_bfq_20 FLOAT,
                ma_bfq_250 FLOAT,
                ma_bfq_30 FLOAT,
                ma_bfq_5 FLOAT,
                ma_bfq_60 FLOAT,
                ma_bfq_90 FLOAT,
                ma_hfq_10 FLOAT,
                ma_hfq_20 FLOAT,
                ma_hfq_250 FLOAT,
                ma_hfq_30 FLOAT,
                ma_hfq_5 FLOAT,
                ma_hfq_60 FLOAT,
                ma_hfq_90 FLOAT,
                ma_qfq_10 FLOAT,
                ma_qfq_20 FLOAT,
                ma_qfq_250 FLOAT,
                ma_qfq_30 FLOAT,
                ma_qfq_5 FLOAT,
                ma_qfq_60 FLOAT,
                ma_qfq_90 FLOAT,
                macd_bfq FLOAT,
                macd_hfq FLOAT,
                macd_qfq FLOAT,
                macd_dea_bfq FLOAT,
                macd_dea_hfq FLOAT,
                macd_dea_qfq FLOAT,
                macd_dif_bfq FLOAT,
                macd_dif_hfq FLOAT,
                macd_dif_qfq FLOAT,
                mass_bfq FLOAT,
                mass_hfq FLOAT,
                mass_qfq FLOAT,
                ma_mass_bfq FLOAT,
                ma_mass_hfq FLOAT,
                ma_mass_qfq FLOAT,
                mfi_bfq FLOAT,
                mfi_hfq FLOAT,
                mfi_qfq FLOAT,
                mtm_bfq FLOAT,
                mtm_hfq FLOAT,
                mtm_qfq FLOAT,
                mtmma_bfq FLOAT,
                mtmma_hfq FLOAT,
                mtmma_qfq FLOAT,
                obv_bfq FLOAT,
                obv_hfq FLOAT,
                obv_qfq FLOAT,
                psy_bfq FLOAT,
                psy_hfq FLOAT,
                psy_qfq FLOAT,
                psyma_bfq FLOAT,
                psyma_hfq FLOAT,
                psyma_qfq FLOAT,
                roc_bfq FLOAT,
                roc_hfq FLOAT,
                roc_qfq FLOAT,
                maroc_bfq FLOAT,
                maroc_hfq FLOAT,
                maroc_qfq FLOAT,
                rsi_bfq_12 FLOAT,
                rsi_bfq_24 FLOAT,
                rsi_bfq_6 FLOAT,
                rsi_hfq_12 FLOAT,
                rsi_hfq_24 FLOAT,
                rsi_hfq_6 FLOAT,
                rsi_qfq_12 FLOAT,
                rsi_qfq_24 FLOAT,
                rsi_qfq_6 FLOAT,
                taq_down_bfq FLOAT,
                taq_down_hfq FLOAT,
                taq_down_qfq FLOAT,
                taq_mid_bfq FLOAT,
                taq_mid_hfq FLOAT,
                taq_mid_qfq FLOAT,
                taq_up_bfq FLOAT,
                taq_up_hfq FLOAT,
                taq_up_qfq FLOAT,
                trix_bfq FLOAT,
                trix_hfq FLOAT,
                trix_qfq FLOAT,
                trma_bfq FLOAT,
                trma_hfq FLOAT,
                trma_qfq FLOAT,
                vr_bfq FLOAT,
                vr_hfq FLOAT,
                vr_qfq FLOAT,
                wr_bfq FLOAT,
                wr_hfq FLOAT,
                wr_qfq FLOAT,
                wr1_bfq FLOAT,
                wr1_hfq FLOAT,
                wr1_qfq FLOAT,
                xsii_td1_bfq FLOAT,
                xsii_td1_hfq FLOAT,
                xsii_td1_qfq FLOAT,
                xsii_td2_bfq FLOAT,
                xsii_td2_hfq FLOAT,
                xsii_td2_qfq FLOAT,
                xsii_td3_bfq FLOAT,
                xsii_td3_hfq FLOAT,
                xsii_td3_qfq FLOAT,
                xsii_td4_bfq FLOAT,
                xsii_td4_hfq FLOAT,
                xsii_td4_qfq FLOAT,
                PRIMARY KEY (ts_code, trade_date)
            )'''
            
            # 首先删除已存在的表
            self.conn.execute('DROP TABLE IF EXISTS stk_factor_pro')
            self.conn.execute(create_table_sql)
            self.conn.commit()
            print("表格 stk_factor_pro 创建成功")
            
        except Exception as e:
            print(f"创建表格 stk_factor_pro 时出错: {e}")
            raise

    def insert_data(self, df):
        """将DataFrame数据插入到表格中"""
        try:
            # 创建临时表
            temp_table_name = 'temp_stk_factor_pro'
            df.to_sql(temp_table_name, self.conn, if_exists='replace', index=False)
            
            # 使用INSERT OR REPLACE将数据从临时表插入到主表
            insert_sql = f'''
                INSERT OR REPLACE INTO stk_factor_pro 
                SELECT * FROM {temp_table_name}
            '''
            self.conn.execute(insert_sql)
            
            # 删除临时表
            self.conn.execute(f'DROP TABLE IF EXISTS {temp_table_name}')
            
            self.conn.commit()
            print(f"成功写入 {len(df)} 条记录")
        except Exception as e:
            print(f"写入数据时出错: {e}")
            self.conn.rollback()

    def close(self):
        """关闭数据库连接"""
        self.conn.close()
        print("数据库连接已关闭")

def get_stock_list():
    """获取股票列表"""
    try:
        df = pro.stock_basic(exchange='', list_status='L')
        return df['ts_code'].tolist()
    except Exception as e:
        print(f"获取股票列表时出错: {e}")
        return []

def get_date_ranges():
    """获取近5年的日期范围，按季度划分"""
    ranges = []
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3*365)  # 3年前
    
    # 将时间范围按季度划分
    current_date = end_date
    while current_date > start_date:
        quarter_end = current_date.strftime('%Y%m%d')
        current_date = current_date - timedelta(days=90)  # 大约一个季度
        quarter_start = current_date.strftime('%Y%m%d')
        ranges.append((quarter_start, quarter_end))
    
    return ranges

def control_request_rate():
    """控制请求频率，确保每分钟不超过30次请求"""
    with request_lock:
        current_time = time.time()
        
        # 移除一分钟之前的请求记录
        while request_times and current_time - request_times[0] >= WINDOW_SIZE:
            request_times.popleft()
        
        # 如果在当前时间窗口内的请求数达到限制
        if len(request_times) >= REQUEST_LIMIT:
            # 计算需要等待的时间
            wait_time = WINDOW_SIZE - (current_time - request_times[0])
            if wait_time > 0:
                time.sleep(wait_time)
                current_time = time.time()  # 更新当前时间
        
        # 记录新的请求时间
        request_times.append(current_time)

def fetch_factor_data(ts_code, start_date, end_date, max_retries=3):
    """获取指定股票和日期范围的技术因子数据"""
    for retry in range(max_retries):
        try:
            print(f"正在获取 {ts_code} 从 {start_date} 至 {end_date} 的技术因子数据...")
            
            # 控制请求频率
            control_request_rate()
            
            # 发起请求
            df = pro.stk_factor_pro(ts_code=ts_code, start_date=start_date, end_date=end_date)
            
            if df is not None and not df.empty:
                return df
            elif retry < max_retries - 1:
                print(f"获取到空数据，将在3秒后重试（第{retry + 1}次）...")
                time.sleep(3)
            else:
                print(f"连续{max_retries}次获取到空数据，跳过处理")
                return None
                
        except Exception as e:
            if retry < max_retries - 1:
                print(f"获取数据出错: {e}，将在3秒后重试（第{retry + 1}次）...")
                time.sleep(3)
            else:
                print(f"获取 {ts_code} 的技术因子数据时出错: {e}")
                return None
    
    return None

def process_stock_data(ts_code, date_ranges, db):
    """处理单个股票的所有时期数据"""
    for start_date, end_date in date_ranges:
        df = fetch_factor_data(ts_code, start_date, end_date)
        if df is not None and not df.empty:
            db.insert_data(df)
            print(f"成功获取 {ts_code} 从 {start_date} 至 {end_date} 的技术因子数据，共 {len(df)} 条记录")
        else:
            print(f"跳过 {ts_code} 从 {start_date} 至 {end_date} 的处理")

def main():
    # 确保数据目录存在
    os.makedirs(os.path.dirname(os.path.join(root_dir, 'data.db')), exist_ok=True)
    
    # 创建数据库实例
    db = StockDatabase()
    
    try:
        # 创建表
        db.create_table()
        
        # 获取股票列表
        stock_list = get_stock_list()
        if not stock_list:
            print("获取股票列表失败")
            return
        
        # 获取日期范围列表
        date_ranges = get_date_ranges()
        print(f"将获取以下日期范围的数据: {date_ranges}")
        
        # 处理每只股票
        total_stocks = len(stock_list)
        for idx, ts_code in enumerate(stock_list, 1):
            print(f"正在处理第 {idx}/{total_stocks} 只股票: {ts_code}")
            process_stock_data(ts_code, date_ranges, db)
            
            # 每处理20只股票后暂停15秒
            if idx % 20 == 0:
                print(f"已处理 {idx} 只股票，暂停15秒...")
                time.sleep(15)
            
    finally:
        # 确保数据库连接被关闭
        db.close()

if __name__ == "__main__":
    print("开始运行股票技术因子数据获取程序...")
    main()
    print("程序运行完成！") 