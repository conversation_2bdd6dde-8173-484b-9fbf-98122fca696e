#!/usr/bin/env python3
"""
脚本包装器 - 为现有脚本添加断点续传功能
"""

import os
import sys
import importlib
import importlib.util
from datetime import datetime, timedelta
import pandas as pd
import sqlite3
from resume_manager import ResumeManager, DataExistenceChecker

# 导入数据进度检测器
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from data_progress_detector import DataProgressDetector

class ScriptWrapper:
    """脚本包装器，添加断点续传功能"""
    
    def __init__(self, script_path, script_name=None):
        self.script_path = script_path
        self.script_name = script_name or os.path.basename(script_path)
        self.resume_manager = ResumeManager()
        self.data_checker = DataExistenceChecker()
        self.progress_detector = DataProgressDetector()
        
    def get_script_config(self):
        """获取脚本配置信息"""
        configs = {
            # 市场数据脚本
            'daily.py': {
                'db_path': 'market_data/daily.db',
                'table_name': 'daily',
                'has_stock_iteration': True,
                'has_date_range': True,
                'batch_processing': True
            },
            'daily_basic.py': {
                'db_path': 'market_data/daily_basic.db',
                'table_name': 'daily_basic',
                'has_stock_iteration': True,
                'has_date_range': True,
                'batch_processing': False
            },
            'adj_factor.py': {
                'db_path': 'market_data/adj_factor.db',
                'table_name': 'adj_factor',
                'has_stock_iteration': True,
                'has_date_range': True,
                'batch_processing': False
            },
            # 基础数据脚本
            'stock_basic.py': {
                'db_path': 'basic_data/stock_basic.db',
                'table_name': 'stock_basic',
                'has_stock_iteration': False,
                'has_date_range': False,
                'batch_processing': False
            },
            'stock_company.py': {
                'db_path': 'basic_data/stock_company.db',
                'table_name': 'stock_company',
                'has_stock_iteration': True,
                'has_date_range': False,
                'batch_processing': False
            },
            # 财务数据脚本
            'income.py': {
                'db_path': 'financial_data/income.db',
                'table_name': 'income',
                'has_stock_iteration': True,
                'has_date_range': True,
                'batch_processing': False
            },
            'balancesheet.py': {
                'db_path': 'financial_data/balancesheet.db',
                'table_name': 'balancesheet',
                'has_stock_iteration': True,
                'has_date_range': True,
                'batch_processing': False
            },
            'cashflow.py': {
                'db_path': 'financial_data/cashflow.db',
                'table_name': 'cashflow',
                'has_stock_iteration': True,
                'has_date_range': True,
                'batch_processing': False
            }
        }
        
        return configs.get(self.script_name, {
            'db_path': f'data/{self.script_name.replace(".py", ".db")}',
            'table_name': self.script_name.replace(".py", ""),
            'has_stock_iteration': True,
            'has_date_range': True,
            'batch_processing': False
        })
    
    def check_resume_possibility(self):
        """检查是否可以断点续传"""
        config = self.get_script_config()
        
        # 检查数据库和表是否存在
        if not os.path.exists(config['db_path']):
            return False, "数据库文件不存在"
        
        if not self.data_checker.check_table_exists(config['db_path'], config['table_name']):
            return False, "数据表不存在"
        
        # 检查是否有进度记录
        progress = self.resume_manager.get_script_progress(self.script_name)
        if not progress:
            return False, "无进度记录"
        
        return True, "可以断点续传"
    
    def get_resume_info(self):
        """获取断点续传信息"""
        can_resume, message = self.check_resume_possibility()
        if not can_resume:
            return None
        
        config = self.get_script_config()
        progress = self.resume_manager.get_script_progress(self.script_name)
        
        # 获取数据统计信息
        data_range = self.data_checker.get_data_date_range(
            config['db_path'], config['table_name']
        )
        
        resume_info = {
            'script_name': self.script_name,
            'last_run_time': progress['last_run_time'],
            'status': progress['status'],
            'progress_percent': round((progress['completed_items'] / progress['total_items'] * 100) 
                                    if progress['total_items'] > 0 else 0, 1),
            'completed_items': progress['completed_items'],
            'total_items': progress['total_items'],
            'failed_items': progress['failed_items'],
            'data_range': data_range,
            'can_resume': True,
            'resume_message': message
        }
        
        return resume_info

    def get_incremental_update_info(self):
        """获取增量更新信息"""
        try:
            # 检测当前数据进度
            progress = self.progress_detector.detect_script_progress(self.script_name)

            # 生成更新计划
            update_plan = self.progress_detector.generate_incremental_update_plan(self.script_name)

            return {
                'current_progress': progress,
                'update_plan': update_plan,
                'can_incremental': update_plan['update_type'] in ['incremental', 'partial', 'none']
            }
        except Exception as e:
            print(f"获取增量更新信息失败: {e}")
            return None

    def should_use_incremental_update(self):
        """判断是否应该使用增量更新"""
        update_info = self.get_incremental_update_info()
        if not update_info:
            return False, "无法获取更新信息"

        update_plan = update_info['update_plan']

        if update_plan['update_type'] == 'none':
            return False, "数据已是最新，无需更新"
        elif update_plan['update_type'] in ['incremental', 'partial']:
            return True, update_plan['reason']
        else:
            return False, "需要完整更新"

    def create_resume_wrapper(self):
        """创建断点续传包装函数"""
        config = self.get_script_config()
        
        def wrapped_main():
            """包装后的main函数"""
            try:
                # 记录开始时间
                start_time = datetime.now()
                self.resume_manager.save_script_progress(
                    self.script_name, 'running', 
                    notes=f"开始时间: {start_time.isoformat()}"
                )
                
                # 导入原始脚本
                original_module = self._import_original_script()
                if not original_module:
                    return {"success": False, "message": "无法导入原始脚本"}
                
                # 如果脚本支持股票迭代，添加跳过逻辑
                if config['has_stock_iteration']:
                    result = self._run_with_stock_resume(original_module, config)
                else:
                    result = self._run_simple_script(original_module)
                
                # 记录完成状态
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                if result.get('success', True):
                    self.resume_manager.save_script_progress(
                        self.script_name, 'completed',
                        notes=f"完成时间: {end_time.isoformat()}, 耗时: {duration:.1f}秒"
                    )
                else:
                    self.resume_manager.save_script_progress(
                        self.script_name, 'failed',
                        notes=f"失败时间: {end_time.isoformat()}, 错误: {result.get('message', '未知错误')}"
                    )
                
                return result
                
            except Exception as e:
                self.resume_manager.save_script_progress(
                    self.script_name, 'failed',
                    notes=f"异常: {str(e)}"
                )
                return {"success": False, "message": f"执行异常: {str(e)}"}
        
        return wrapped_main
    
    def _import_original_script(self):
        """导入原始脚本"""
        try:
            # 获取脚本目录和文件名
            script_dir = os.path.dirname(self.script_path)
            script_filename = os.path.basename(self.script_path)
            module_name = script_filename[:-3]  # 去掉.py后缀
            
            # 确保脚本目录在Python路径中
            if script_dir not in sys.path:
                sys.path.insert(0, script_dir)
            
            # 动态导入模块
            spec = importlib.util.spec_from_file_location(module_name, self.script_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            return module
        except Exception as e:
            print(f"导入脚本失败: {e}")
            return None
    
    def _run_with_stock_resume(self, module, config):
        """运行支持股票迭代的脚本"""
        try:
            # 获取已完成的股票列表
            completed_stocks = self.resume_manager.get_completed_stocks(self.script_name)
            
            # 修改模块中的股票处理逻辑
            if hasattr(module, 'main'):
                # 这里需要根据具体脚本的结构来调整
                # 暂时直接调用原始main函数
                result = module.main()
                return {"success": True, "message": "执行完成"}
            else:
                return {"success": False, "message": "脚本中没有main函数"}
                
        except Exception as e:
            return {"success": False, "message": f"执行出错: {str(e)}"}
    
    def _run_simple_script(self, module):
        """运行简单脚本"""
        try:
            if hasattr(module, 'main'):
                result = module.main()
                return {"success": True, "message": "执行完成"}
            else:
                return {"success": False, "message": "脚本中没有main函数"}
        except Exception as e:
            return {"success": False, "message": f"执行出错: {str(e)}"}

def create_resume_dialog(main_panel, script_info, incremental_info=None):
    """创建增强的断点续传对话框"""
    import tkinter as tk
    from tkinter import messagebox, ttk

    def on_resume():
        """选择断点续传"""
        dialog.result = 'resume'
        dialog.destroy()

    def on_restart():
        """选择重新开始"""
        dialog.result = 'restart'
        dialog.destroy()

    def on_incremental():
        """选择增量更新"""
        dialog.result = 'incremental'
        dialog.destroy()

    def on_cancel():
        """取消执行"""
        dialog.result = 'cancel'
        dialog.destroy()
    
    # 创建对话框
    dialog = tk.Toplevel(main_panel.root)
    dialog.title("断点续传")
    dialog.geometry("500x400")
    dialog.transient(main_panel.root)
    dialog.grab_set()
    dialog.result = 'cancel'
    
    # 居中显示
    dialog.geometry("+%d+%d" % (
        main_panel.root.winfo_rootx() + 50,
        main_panel.root.winfo_rooty() + 50
    ))
    
    # 主框架
    main_frame = ttk.Frame(dialog, padding="10")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="检测到未完成的数据获取任务", 
                           font=('Arial', 12, 'bold'))
    title_label.pack(pady=(0, 10))
    
    # 信息显示区域
    info_frame = ttk.LabelFrame(main_frame, text="任务信息", padding="10")
    info_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
    
    # 创建信息文本
    info_text = tk.Text(info_frame, height=12, wrap=tk.WORD)
    scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=info_text.yview)
    info_text.configure(yscrollcommand=scrollbar.set)
    
    info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 填充信息
    info_content = f"""脚本名称: {script_info['script_name']}
上次运行时间: {script_info['last_run_time']}
任务状态: {script_info['status']}
完成进度: {script_info['progress_percent']}% ({script_info['completed_items']}/{script_info['total_items']})
失败项目: {script_info['failed_items']}

数据库信息:
"""

    if script_info['data_range']:
        info_content += f"""- 数据起始日期: {script_info['data_range']['start_date']}
- 数据结束日期: {script_info['data_range']['end_date']}
- 记录总数: {script_info['data_range']['record_count']}
"""
    else:
        info_content += "- 暂无数据记录\n"

    # 添加增量更新信息
    if incremental_info:
        update_plan = incremental_info['update_plan']
        current_progress = incremental_info['current_progress']

        info_content += f"""
增量更新分析:
- 当前数据进度: {current_progress.get('progress_percent', 0):.1f}%
- 已有股票数据: {current_progress.get('completed_stocks', 0)}只
- 缺失股票数据: {len(current_progress.get('missing_stocks', []))}只
- 更新类型: {update_plan['update_type']}
- 更新原因: {update_plan['reason']}
- 预估时间: {update_plan['estimated_time']}
"""

    info_content += f"""
选择操作:
• 断点续传: 从上次中断的地方继续获取数据
• 重新开始: 清除进度记录，从头开始获取数据"""

    if incremental_info and incremental_info['can_incremental']:
        info_content += """
• 增量更新: 智能检测缺失数据，只获取需要的部分"""

    info_content += """
• 取消: 不执行此脚本
"""
    
    info_text.insert(tk.END, info_content)
    info_text.config(state=tk.DISABLED)
    
    # 按钮框架
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=(10, 0))
    
    # 按钮
    resume_btn = ttk.Button(button_frame, text="断点续传", command=on_resume)
    resume_btn.pack(side=tk.LEFT, padx=(0, 5))

    restart_btn = ttk.Button(button_frame, text="重新开始", command=on_restart)
    restart_btn.pack(side=tk.LEFT, padx=5)

    # 只有在支持增量更新时才显示增量更新按钮
    if incremental_info and incremental_info['can_incremental']:
        incremental_btn = ttk.Button(button_frame, text="增量更新", command=on_incremental)
        incremental_btn.pack(side=tk.LEFT, padx=5)

    cancel_btn = ttk.Button(button_frame, text="取消", command=on_cancel)
    cancel_btn.pack(side=tk.RIGHT)
    
    # 等待用户选择
    dialog.wait_window()
    return getattr(dialog, 'result', 'cancel')

def main():
    """测试函数"""
    wrapper = ScriptWrapper("market_data/daily.py")
    
    # 检查断点续传可能性
    can_resume, message = wrapper.check_resume_possibility()
    print(f"可以断点续传: {can_resume}, 消息: {message}")
    
    if can_resume:
        resume_info = wrapper.get_resume_info()
        print("断点续传信息:", resume_info)

if __name__ == "__main__":
    main()
