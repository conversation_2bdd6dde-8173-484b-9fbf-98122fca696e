#!/usr/bin/env python3
"""
数据进度检测器 - 检测每个脚本的数据获取进度，实现智能增量更新
"""

import os
import sqlite3
import glob
from datetime import datetime, timedelta
import pandas as pd

class DataProgressDetector:
    """数据进度检测器"""
    
    def __init__(self):
        self.script_db_mapping = {
            # 市场数据
            'daily.py': 'market_data/daily.db',
            'weekly.py': 'market_data/weekly.db', 
            'monthly.py': 'market_data/monthly.db',
            'pro_bar.py': 'market_data/pro_bar.db',
            'adj_factor.py': 'market_data/adj_factor.db',
            'daily_basic.py': 'market_data/daily_basic.db',
            
            # 基础数据
            'stock_basic.py': 'basic_data/stock_basic.db',
            'stock_company.py': 'basic_data/stock_company.db',
            'trade_cal.py': 'basic_data/trade_cal.db',
            
            # 财务数据
            'income.py': 'financial_data/income.db',
            'balancesheet.py': 'financial_data/balancesheet.db',
            'cashflow.py': 'financial_data/cashflow.db',
            'fina_indicator.py': 'financial_data/fina_indicator.db',
            
            # 资金流向
            'moneyflow.py': 'moneyflow_data/moneyflow.db',
            'moneyflow_hsgt.py': 'moneyflow_data/moneyflow_hsgt.db',
            
            # 其他数据
            'realtime_quote.py': 'market_data/realtime_quote.db',
            'realtime_tick.py': 'market_data/realtime_tick.db'
        }
    
    def get_stock_list(self):
        """获取股票列表"""
        try:
            stock_basic_db = 'basic_data/stock_basic.db'
            if os.path.exists(stock_basic_db):
                conn = sqlite3.connect(stock_basic_db)
                df = pd.read_sql("SELECT ts_code FROM stock_basic WHERE list_status='L'", conn)
                conn.close()
                return df['ts_code'].tolist()
            else:
                print("❌ 股票基础数据库不存在，无法获取股票列表")
                return []
        except Exception as e:
            print(f"❌ 获取股票列表失败: {e}")
            return []
    
    def detect_script_progress(self, script_name):
        """检测单个脚本的数据进度"""
        db_path = self.script_db_mapping.get(script_name)
        if not db_path or not os.path.exists(db_path):
            return {
                'script_name': script_name,
                'db_exists': False,
                'total_stocks': 0,
                'completed_stocks': 0,
                'missing_stocks': [],
                'latest_date': None,
                'earliest_date': None,
                'total_records': 0,
                'progress_percent': 0
            }
        
        try:
            conn = sqlite3.connect(db_path)
            
            # 获取表名（通常与脚本名相同，去掉.py后缀）
            table_name = script_name.replace('.py', '')
            
            # 检查表是否存在
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if not cursor.fetchone():
                conn.close()
                return {
                    'script_name': script_name,
                    'db_exists': True,
                    'table_exists': False,
                    'total_stocks': 0,
                    'completed_stocks': 0,
                    'missing_stocks': [],
                    'progress_percent': 0
                }
            
            # 获取数据统计
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            total_records = cursor.fetchone()[0]
            
            # 获取已有数据的股票数量
            cursor.execute(f"SELECT COUNT(DISTINCT ts_code) FROM {table_name}")
            completed_stocks = cursor.fetchone()[0]
            
            # 获取日期范围
            try:
                cursor.execute(f"SELECT MIN(trade_date), MAX(trade_date) FROM {table_name}")
                date_range = cursor.fetchone()
                earliest_date, latest_date = date_range if date_range[0] else (None, None)
            except:
                earliest_date, latest_date = None, None
            
            # 获取已有数据的股票列表
            cursor.execute(f"SELECT DISTINCT ts_code FROM {table_name}")
            existing_stocks = set(row[0] for row in cursor.fetchall())
            
            conn.close()
            
            # 获取全部股票列表
            all_stocks = set(self.get_stock_list())
            total_stocks = len(all_stocks)
            
            # 计算缺失的股票
            missing_stocks = list(all_stocks - existing_stocks)
            
            progress_percent = (completed_stocks / total_stocks * 100) if total_stocks > 0 else 0
            
            return {
                'script_name': script_name,
                'db_exists': True,
                'table_exists': True,
                'total_stocks': total_stocks,
                'completed_stocks': completed_stocks,
                'missing_stocks': missing_stocks,
                'latest_date': latest_date,
                'earliest_date': earliest_date,
                'total_records': total_records,
                'progress_percent': round(progress_percent, 1)
            }
            
        except Exception as e:
            print(f"❌ 检测 {script_name} 进度时出错: {e}")
            return {
                'script_name': script_name,
                'error': str(e),
                'progress_percent': 0
            }
    
    def detect_all_scripts_progress(self):
        """检测所有脚本的数据进度"""
        results = {}
        
        print("🔍 检测所有脚本的数据进度...")
        print("=" * 80)
        
        for script_name in self.script_db_mapping.keys():
            progress = self.detect_script_progress(script_name)
            results[script_name] = progress
            
            # 显示进度信息
            if progress.get('db_exists', False) and progress.get('table_exists', False):
                print(f"📊 {script_name:<20} "
                      f"进度: {progress['progress_percent']:>6.1f}% "
                      f"({progress['completed_stocks']:>4}/{progress['total_stocks']:<4}) "
                      f"记录: {progress['total_records']:>8} "
                      f"日期: {progress['latest_date'] or 'N/A'}")
            else:
                status = "数据库不存在" if not progress.get('db_exists') else "表不存在"
                print(f"❌ {script_name:<20} {status}")
        
        return results
    
    def get_missing_stocks_for_script(self, script_name, limit=None):
        """获取指定脚本缺失的股票列表"""
        progress = self.detect_script_progress(script_name)
        missing_stocks = progress.get('missing_stocks', [])
        
        if limit and len(missing_stocks) > limit:
            return missing_stocks[:limit]
        
        return missing_stocks
    
    def get_last_update_date(self, script_name, ts_code=None):
        """获取最后更新日期"""
        db_path = self.script_db_mapping.get(script_name)
        if not db_path or not os.path.exists(db_path):
            return None
        
        try:
            conn = sqlite3.connect(db_path)
            table_name = script_name.replace('.py', '')
            
            if ts_code:
                cursor = conn.cursor()
                cursor.execute(f"SELECT MAX(trade_date) FROM {table_name} WHERE ts_code=?", (ts_code,))
                result = cursor.fetchone()
                conn.close()
                return result[0] if result and result[0] else None
            else:
                cursor = conn.cursor()
                cursor.execute(f"SELECT MAX(trade_date) FROM {table_name}")
                result = cursor.fetchone()
                conn.close()
                return result[0] if result and result[0] else None
                
        except Exception as e:
            print(f"❌ 获取 {script_name} 最后更新日期失败: {e}")
            return None
    
    def generate_incremental_update_plan(self, script_name):
        """生成增量更新计划"""
        progress = self.detect_script_progress(script_name)
        
        if not progress.get('db_exists') or not progress.get('table_exists'):
            return {
                'update_type': 'full',
                'reason': '数据库或表不存在，需要完整获取',
                'stocks_to_process': self.get_stock_list(),
                'estimated_time': 'Unknown'
            }
        
        missing_stocks = progress['missing_stocks']
        latest_date = progress['latest_date']
        
        # 判断更新策略
        if len(missing_stocks) == 0:
            # 没有缺失股票，检查是否需要更新最新数据
            if latest_date:
                latest_dt = datetime.strptime(latest_date, '%Y%m%d')
                days_behind = (datetime.now() - latest_dt).days
                
                if days_behind <= 1:
                    return {
                        'update_type': 'none',
                        'reason': '数据已是最新，无需更新',
                        'stocks_to_process': [],
                        'estimated_time': '0分钟'
                    }
                else:
                    return {
                        'update_type': 'incremental',
                        'reason': f'数据落后{days_behind}天，需要增量更新',
                        'stocks_to_process': self.get_stock_list(),
                        'start_date': latest_date,
                        'estimated_time': f'{len(self.get_stock_list()) * 0.5:.0f}分钟'
                    }
            else:
                return {
                    'update_type': 'full',
                    'reason': '无法确定最新日期，建议完整更新',
                    'stocks_to_process': self.get_stock_list(),
                    'estimated_time': f'{len(self.get_stock_list()) * 1:.0f}分钟'
                }
        else:
            # 有缺失股票，需要补充
            return {
                'update_type': 'partial',
                'reason': f'有{len(missing_stocks)}只股票缺失数据',
                'stocks_to_process': missing_stocks,
                'estimated_time': f'{len(missing_stocks) * 1:.0f}分钟'
            }

def main():
    """主函数"""
    detector = DataProgressDetector()
    
    print("📊 数据进度检测和增量更新计划")
    print("=" * 60)
    
    # 检测所有脚本进度
    all_progress = detector.detect_all_scripts_progress()
    
    print("\n" + "=" * 60)
    print("📋 增量更新建议:")
    
    # 为每个脚本生成更新计划
    for script_name in ['daily.py', 'weekly.py', 'monthly.py', 'daily_basic.py']:
        plan = detector.generate_incremental_update_plan(script_name)
        print(f"\n🔧 {script_name}:")
        print(f"   更新类型: {plan['update_type']}")
        print(f"   原因: {plan['reason']}")
        print(f"   需处理股票: {len(plan['stocks_to_process'])}只")
        print(f"   预估时间: {plan['estimated_time']}")
        
        if plan['update_type'] == 'partial' and len(plan['stocks_to_process']) <= 10:
            print(f"   缺失股票: {plan['stocks_to_process'][:5]}...")

if __name__ == "__main__":
    main()
