import os
import sys
import time
import pandas as pd
import sqlite3
import tushare as ts
from datetime import datetime, timedelta
import logging

# Get the project root directory and add it to Python path
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, root_dir)

from config import get_token

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stk_daily.log'),
        logging.StreamHandler()
    ]
)

class StockDailyDatabase:
    def __init__(self, db_path):
        self.db_path = db_path
        self.conn = None
        self.cursor = None
        
    def connect(self):
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.cursor = self.conn.cursor()
        except sqlite3.Error as e:
            logging.error(f"数据库连接错误: {e}")
            raise
            
    def create_table(self):
        try:
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS stk_daily (
                    ts_code TEXT,
                    trade_date TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change REAL,
                    pct_chg REAL,
                    vol REAL,
                    amount REAL,
                    PRIMARY KEY (ts_code, trade_date)
                )
            """)
            self.conn.commit()
        except sqlite3.Error as e:
            logging.error(f"创建表格错误: {e}")
            raise
    
    def get_latest_date(self):
        try:
            self.cursor.execute("SELECT MAX(trade_date) FROM stk_daily")
            result = self.cursor.fetchone()[0]
            return result if result else None
        except sqlite3.Error as e:
            logging.error(f"获取最新日期错误: {e}")
            return None
            
    def insert_data(self, df):
        if df.empty:
            return
            
        temp_table = 'temp_daily'
        try:
            # 创建临时表
            df.to_sql(temp_table, self.conn, if_exists='replace', index=False)
            
            # 从临时表插入数据到主表
            self.cursor.execute(f"""
                INSERT OR REPLACE INTO stk_daily 
                SELECT * FROM {temp_table}
            """)
            
            # 删除临时表
            self.cursor.execute(f"DROP TABLE {temp_table}")
            
            self.conn.commit()
        except sqlite3.Error as e:
            logging.error(f"插入数据错误: {e}")
            self.conn.rollback()
            raise
        finally:
            try:
                self.cursor.execute(f"DROP TABLE IF EXISTS {temp_table}")
                self.conn.commit()
            except:
                pass
    
    def check_data_integrity(self, date):
        """检查指定日期的数据完整性"""
        try:
            self.cursor.execute("""
                SELECT COUNT(*) FROM stk_daily WHERE trade_date = ?
            """, (date,))
            count = self.cursor.fetchone()[0]
            return count > 0
        except sqlite3.Error as e:
            logging.error(f"检查数据完整性错误: {e}")
            return False
                
    def close(self):
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()

def fetch_daily_data(pro, trade_date):
    try:
        df = pro.daily(trade_date=trade_date)
        time.sleep(0.5)  # API调用频率限制
        return df
    except Exception as e:
        logging.error(f"获取{trade_date}数据错误: {e}")
        return pd.DataFrame()

def main():
    # 确保数据目录存在
    data_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    db_path = os.path.join(data_dir, 'data.db')
    
    # 初始化Tushare
    ts.set_token(get_token())
    pro = ts.pro_api()
    
    # 创建数据库实例
    db = StockDailyDatabase(db_path)
    
    try:
        # 连接数据库并创建表
        db.connect()
        db.create_table()
        
        # 获取最新的已保存数据日期
        latest_saved_date = db.get_latest_date()
        
        # 获取过去5年的交易日历
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=3*365)).strftime('%Y%m%d')
        
        if latest_saved_date:
            start_date = latest_saved_date
            logging.info(f"从上次中断的位置继续: {start_date}")
        
        cal_df = pro.trade_cal(start_date=start_date, end_date=end_date, is_open=1)
        trade_dates = cal_df['cal_date'].tolist()
        
        total_dates = len(trade_dates)
        logging.info(f"开始获取{total_dates}个交易日的数据...")
        
        for i, date in enumerate(trade_dates, 1):
            if db.check_data_integrity(date):
                logging.info(f"日期 {date} 的数据已存在且完整，跳过")
                continue
                
            logging.info(f"处理日期 {date} ({i}/{total_dates})")
            df = fetch_daily_data(pro, date)
            if not df.empty:
                db.insert_data(df)
            else:
                logging.warning(f"日期 {date} 没有获取到数据")
                
        logging.info("数据获取完成！")
        
    except Exception as e:
        logging.error(f"发生错误: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    main() 